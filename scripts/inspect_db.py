from sqlalchemy import create_engine, inspect
import os
import pandas as pd
from dotenv import load_dotenv
load_dotenv()  # take environment variables from .env.
DB_HOST = os.getenv("DB_HOST")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")
DB_PORT = os.getenv("DB_PORT", "5432")
# Construct the connection string (modify with your details)
connection_string = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(connection_string)
# Use SQLAlchemy's inspector to get table names
inspector = inspect(engine)
tables = inspector.get_table_names()
print("Tables in the database:", tables)
# query = """SELECT date,quantity,hs_code,std_qty,origin_port,unit_rate_usd,fob_value_usd,is_import,country_name,hs_code_description,global_unit_id,global_std_unit_id,parent_global_exporter_id,universal_exporter_id,parent_global_importer_id,universal_importer_id,parent_coo,parent_cod,parent_poo,parent_pod,universal_poo,universal_pod,universal_unit_id,universal_std_unit_id,shipper_city,shipper_pin,shipper_state,shipper_country,consignee_city,consignee_pin,consignee_state,consignee_country,coo_continent_id,cod_continent_id,product_desc
# FROM volza_trade_data"""
# Let's check exact country names in the database
query = """
SELECT DISTINCT parent_cod
FROM volza_trade_data
WHERE parent_cod LIKE '%United%' OR parent_cod LIKE '%States%' OR parent_cod LIKE '%USA%' OR parent_cod LIKE '%US%'
ORDER BY parent_cod
"""
df = pd.read_sql(query, engine)
print(df.head())
print(df.shape)
