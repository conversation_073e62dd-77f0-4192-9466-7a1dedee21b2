# pricing-engine
Microservice for the Pricing AI Engine

### Prerequisites
1. python3 - https://www.python.org/downloads/ (python version <4.0, >=3.10 for aisuite)
2. pip3
3. Postgresql 
4. podman - https://podman.io/
5. ...

### Local Setup
#### Set Configurations
The following configurations are required to run the service. Create the db in the postgres and add appropriate permissions to db user prior to application start
```sh
export DATABASE_URL=postgresql://HOST:5432/qna?sslmode=require
```

#### Run Migrations
This project uses alembic migrations to apply and run migrations 
```sh
flask db migrate -m "<file name for migration>"
flask db upgrade
# Downgrade
flask db downgrade
```

#### Run Command 
Before running locally, apply the migrations to DB using the above commands.
```sh
cd <PROJECT_ROOT_FOLDER>
python3 -m venv pricing-engine-env
source pricing-engine-env/bin/activate
pip install --no-cache-dir -r requirements.txt
# There are some big files in this repo which might need installing and running git large file storage
brew install git-lfs
git lfs install
git lfs pull
python3 main.py
```
Navigate to http://127.0.0.1:5001/pricing/docs for accessing API documentation

To Freeze any dependencies installed, use the following command
```sh 
pip freeze > requirements.txt
```


### Prod deployment using Podman 
Using podman container tools for building images, the container runs on 8080 port. It uses gunicorn webserver for prod, the configurations for the same can be found in gunicorn_config.py in root folder. At present it uses 2 processes and 4 threads for serving requests
```sh
cd <PROJECT_ROOT_FOLDER>
podman build . -t pricing-engine
podman run -p 8080:8080 -e DATABASE_URL=postgresql://HOST:5432/qna?sslmode=require -e UI_BASE_URL=http://localhost:5173/?code=  pricing-engine
```

### API Documentation
API documentation is available at:
- Development: http://localhost:5001/pricing/docs
- Production: http://<HOST>:8080/pricing/docs

### Prometheus Metrics for Monitoring
Metric apis is exposed at url http(s)://\<HOST>:\<PORT>/metrics it can be used for monitoring using prometheus and grafana

### Load Testing
Load testing uses the k6 module to run the tests, tests are under the scripts folder which are implemented using javascript.
To Run:
```sh 
k6 run scripts/load_testing/load_test.js
```

#### Test Setup:
Server: 16GB, 8 Core M1 Chip 
Database: 16 GB, 4 CPU GCP
No Spike in CPU for DB or Server, tests are conducted while running in dev mode.

#### Test Results:

## Prod Env
```sh
sudo docker build -t pricing-engine .
sudo docker stop pricing-engine
sudo docker rm pricing-engine
sudo docker run --name pricing-engine -d -p 8080:8080 pricing-engine
sudo docker system prune -a -f
```
