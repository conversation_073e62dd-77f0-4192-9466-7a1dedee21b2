import csv
import os.path

import sys
import os
from dotenv import load_dotenv
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.tariff_service import *  
from src.tariff.extract_hts import *
from src.agents.tariff_extractor_agent import *



def standardize_code_hts(code: str) -> str:
    # Remove all non-numeric characters
    numbers = ''.join(char for char in code if char.isdigit())
    
    # Pad with zeros if less than 10 digits
    if len(numbers) <= 12:
        numbers = numbers.ljust(10, '0')
    elif len(numbers) > 12:
        raise ValueError("Input contains more than 10 digits")
    
    # Format into the desired pattern
    return f"{numbers[0:4]}.{numbers[4:6]}.{numbers[6:8]}.{numbers[8:10]}"

def load_indices():
    print("Loading pre-computed indices...")
    indices = HTSIndices.load("hts_indices", auto_create=True, data_file='htsdata_all.json')
    lookup = HTSSemanticLookup(indices)
    return lookup

def get_tarff_country(lookup,hs_code,country):

        tf_service = TariffService()              
        print(hs_code)
        standardized_hts_code = standardize_code_hts(hs_code)  
        print(standardized_hts_code)
        tariff_results = lookup.lookup_hts(standardized_hts_code)
        print(f"tariff_results; {tariff_results}")
        tariff_results_list=[tariff_results]  

        Standard_duty, Incremental_duty, Proposed_duty, ad_value, cd_value = tf_service.get_country_tariff(tariff_results_list,country)

        json_blob={}
        json_blob['Country']=country
        json_blob['Standard Tariff']=Standard_duty
        json_blob['New Tariff']=Incremental_duty
        json_blob['Proposed Tariff']=Proposed_duty
        tariff_response = extract_tariff_info_structured_assist_openAI(json_blob) 
        return tariff_response

if __name__=="__main__":
    lookup=load_indices()
    tariff_response = get_tarff_country(lookup,'2933.59.90.00',"India")#'2933.59.90.00',"3907.30.00.00"
    print(tariff_response)
               
