import pandas as pd
import sys
import os
import yaml
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from src.tariff.extract_hts import *
#from src.volza.extract_hts_volza import *
from src.tariff.hts_duty_calculator import *
from src.tariff.hts_duty_calculator_allCountries import *

def standardize_code(code: str) -> str:
    # Remove all non-numeric characters
    numbers = ''.join(char for char in code if char.isdigit())
    
    # Pad with zeros if less than 10 digits
    if len(numbers) < 10:
        numbers = numbers.ljust(10, '0')
    elif len(numbers) > 10:
        raise ValueError("Input contains more than 10 digits")
    
    # Format into the desired pattern
    return f"{numbers[0:4]}.{numbers[4:6]}.{numbers[6:8]}.{numbers[8:10]}"

def calculate_duty_rate(product_name, country_code, hts_number):
    # Add config loading at the start of the function
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'tariff_config.yaml')
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    print("Invoking Duty Calculator via 3 sources")
    duty={} #
    # Source 1: Tariff DB
    INDEX_DIR = "hts_indices_htsTariff"
    # Use the enhanced load method with auto-creation
    indices = HTSIndices.load(INDEX_DIR, auto_create=True, data_file='htsdata_all.json')

    # Create lookup system with loaded indices
    lookup = HTSSemanticLookup(indices)
    
    # Example searches
    semantic_results = lookup.semantic_search(product_name)
    print(semantic_results[0])
    if len(semantic_results) == 0:
        duty_semantics_Tariff="Null"
    #exit(1)

    tariff_df ={}
    tariff_semantic_similarity_score = semantic_results[0]['similarity_score']
    tariff_df['similarity_score'] = tariff_semantic_similarity_score
    tariff_df['htsno'] = semantic_results[0]['hts_code']
    tariff_df['hts_code_description'] = semantic_results[0]['main_details']['description']
    print(tariff_semantic_similarity_score)
    #exit(1)
    if (tariff_semantic_similarity_score>=0.50):
        top_k_results = 1
        duty_semantics_Tariff = parse_hts_data_all_countries(semantic_results[:top_k_results])
        tariff_df['tariff_info'] = duty_semantics_Tariff  # This will now contain footer_duty instead of anti_dumping_duty
    else:
        tariff_df['tariff_info'] = "Null"
        
    duty["TariffDB"]=tariff_df
    print("************Tariff*******")
    #print(duty_semantics_Tariff)
    #exit(1)
    #source 2:
    hts_code = standardize_code(hts_number)
    print(hts_code)
    perplexity_results = lookup.lookup_hts(hts_code)
    if perplexity_results==None:
        return "Sorry, I am not able to retrieve the tariff names. Try providing an accurate product name or HS Code. Thanks "

    #print(perplexity_results)
    perplexity_results_list=[perplexity_results]
    duty_perplexity = parse_hts_data_all_countries(perplexity_results_list)    

    perplexity_df ={}
    perplexity_df['hts_code'] = semantic_results[0]['hts_code']    
    perplexity_df["tariif_info"]=duty_perplexity
    duty["Perplexity"]= perplexity_df
    print("************Perplexity*******")
    #print(duty_perplexity)
    #exit(1)

    # Source 3: Volza (optional - skip if data not available)
    try:
        VOLZA_INDEX_DIR = "hts_indices_volza"
        VOLZA_DATA_FILE = "volza_trade_data_semantic_summary_13022025_final.json"

        # Check if Volza data file exists
        if not os.path.exists(VOLZA_DATA_FILE):
            print("Volza data file not found. Skipping Volza analysis.")
            volza_df = {
                'similarity_score': 0.0,
                'hs_code': 'N/A',
                'hs_code_description': 'Volza data not available',
                'product_details': 'N/A',
                'tariff_info': 'Null'
            }
            duty["Volza"] = volza_df
        else:
            # Try to use Volza indices (this would require implementing Volza classes)
            # For now, we'll skip this functionality
            print("Volza functionality not yet implemented. Skipping Volza analysis.")
            volza_df = {
                'similarity_score': 0.0,
                'hs_code': 'N/A',
                'hs_code_description': 'Volza functionality not implemented',
                'product_details': 'N/A',
                'tariff_info': 'Null'
            }
            duty["Volza"] = volza_df
    except Exception as e:
        print(f"Error in Volza processing: {e}. Skipping Volza analysis.")
        volza_df = {
            'similarity_score': 0.0,
            'hs_code': 'N/A',
            'hs_code_description': f'Volza error: {str(e)}',
            'product_details': 'N/A',
            'tariff_info': 'Null'
        }
        duty["Volza"] = volza_df
    print("************Volza*******")
    #print(duty_semantics_Tariff)

    # Incremental_duty = 0
    # if country_code ==  "CN":
    #     Incremental_duty="Trumps recent announcement implies 10% additional duty on China"
    Incremental_duty = "NA"
    if country_code in config['incremental_duties']:
        Incremental_duty = config['incremental_duties'][country_code]['description']
   
    return {"standard duty":duty,"recent announcement of new_duty":Incremental_duty }

# Example usage:
if __name__=="__main__":

    product_name = "MEA Triazine" #"epoxy resins"
    country_code = 'CH'  # Australia

    duty_response = calculate_duty_rate(product_name, country_code,"29336100")
    print(duty_response)
