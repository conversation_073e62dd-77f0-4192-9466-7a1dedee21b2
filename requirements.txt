aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
aisuite==0.1.8
alembic==1.14.1
aniso8601==10.0.0
annotated-types==0.7.0
anyio==4.8.0
attrs==25.1.0
beautifulsoup4==4.12.3
blinker==1.9.0
boto3==1.36.16
botocore==1.36.16
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
dataclasses-json==0.6.7
distro==1.9.0
filelock==3.17.0
Flask==3.1.0
Flask-Cors==5.0.0
Flask-Migrate==4.1.0
flask-restx==1.3.0
Flask-SQLAlchemy==3.1.1
frozenlist==1.5.0
fsspec==2025.2.0
gunicorn==23.0.0
h11==0.14.0
httpcore==1.0.7
httpx==0.27.2
httpx-sse==0.4.0
huggingface-hub==0.28.1
idna==3.10
importlib_resources==6.5.2
itsdangerous==2.2.0
Jinja2==3.1.5
jiter==0.8.2
jmespath==1.0.1
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
langchain==0.3.17
langchain-community==0.3.16
langchain-core==0.3.33
langchain-experimental==0.3.4
langchain-openai==0.3.3
langchain-text-splitters==0.3.5
langsmith==0.3.4
Mako==1.3.9
MarkupSafe==3.0.2
marshmallow==3.26.1
mpmath==1.3.0
multidict==6.1.0
mypy-extensions==1.0.0
networkx==3.4.2
numpy==2.2.2
openai==1.61.0
orjson==3.10.15
packaging==24.2
pandas==2.2.3
pillow==11.1.0
prometheus_client==0.21.1
prometheus_flask_exporter==0.23.1
propcache==0.2.1
psycopg2-binary==2.9.10
pydantic==2.10.6
pydantic-settings==2.7.1
pydantic_core==2.27.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2025.1
PyYAML==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rpds-py==0.22.3
s3transfer==0.11.2
safetensors==0.5.2
scikit-learn==1.6.1
scipy==1.15.1
sentence-transformers==3.4.1
setuptools==75.8.0
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.37
sympy==1.13.1
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.8.0
tokenizers==0.21.0
torch==2.6.0
tqdm==4.67.1
transformers==4.48.3
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
Werkzeug==3.1.3
yarl==1.18.3
zstandard==0.23.0
psutil==7.0.0