name: Deploy Pricing Engine to ECS

on:
  push:
    branches:
      - main  # Trigger on push to main branch

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Log in to Amazon ECR
        run: |
          aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 087707940088.dkr.ecr.us-east-1.amazonaws.com

      - name: Git LFS Install & Pull
        run: |
          git lfs install
          git lfs pull

      - name: Build, Tag, and Push Docker Image
        run: |
          docker build --platform linux/amd64 -t 087707940088.dkr.ecr.us-east-1.amazonaws.com/pricing-engine:latest .
          docker push 087707940088.dkr.ecr.us-east-1.amazonaws.com/pricing-engine:latest

      - name: Update ECS Service
        run: |
          aws ecs update-service --cluster pricing-engine --service pricing-engine-01 --force-new-deployment
