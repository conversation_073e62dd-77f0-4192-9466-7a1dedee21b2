# Pricing Engine - Quick Start Reference

## 🚀 One-Time Setup

```bash
# 1. Ensure Python 3.11 is installed
python3.11 --version

# 2. Create and activate virtual environment
python3.11 -m venv pricing-engine-env
source pricing-engine-env/bin/activate

# 3. Install dependencies (in order)
pip install --upgrade pip
pip install flask flask-cors flask-migrate flask-restx flask-sqlalchemy gunicorn psycopg2-binary python-dotenv alembic prometheus-flask-exporter
pip install langchain langchain-openai langchain-core langchain-community
pip install torch torchvision torchaudio sentence-transformers scikit-learn pandas boto3
pip install beautifulsoup4 lxml redis psutil

# 4. Setup database
flask db upgrade
```

## 🏃‍♂️ Daily Startup (2 Terminals)

### Terminal 1: HTS2Duty Service
```bash
cd hts2duty
source ../pricing-engine-env/bin/activate
python api.py
```
**Wait for**: `Running on http://127.0.0.1:8080`

### Terminal 2: Main Application
```bash
source pricing-engine-env/bin/activate
python main.py
```
**Wait for**: `Running on http://127.0.0.1:5001`

## 🔗 Access Points

- **API Documentation**: http://localhost:5001/pricing/docs
- **Health Check**: http://localhost:5001/pricing/health
- **Metrics**: http://localhost:5001/metrics
- **HTS2Duty**: http://localhost:8080/tariff?hs_code=**********&country=India

## 🛠️ Quick Troubleshooting

### Port Issues
```bash
# Kill process on port 8080
lsof -i :8080
kill <PID>

# Kill process on port 5001
lsof -i :5001
kill <PID>
```

### Environment Issues
```bash
# Recreate environment
rm -rf pricing-engine-env
python3.11 -m venv pricing-engine-env
source pricing-engine-env/bin/activate
# Re-run installation steps
```

### Missing Dependencies
```bash
source pricing-engine-env/bin/activate
pip install <missing-package>
```

## 📋 Checklist

**Before Starting:**
- [ ] Python 3.11 installed
- [ ] Virtual environment created
- [ ] All dependencies installed
- [ ] Database migrations run
- [ ] `.env` file configured

**Startup Success Indicators:**
- [ ] HTS2Duty: "Redis flushed successfully"
- [ ] HTS2Duty: "Running on http://127.0.0.1:8080"
- [ ] Main App: "Loaded historical data with 170 records"
- [ ] Main App: "Running on http://127.0.0.1:5001"
- [ ] API docs accessible at localhost:5001/pricing/docs

## 🆘 Common Errors

| Error | Solution |
|-------|----------|
| `ModuleNotFoundError` | Install missing package with pip |
| `Address already in use` | Kill process using the port |
| `Database connection failed` | Check `.env` configuration |
| `Python version not supported` | Use Python 3.11 |
| `Redis connection failed` | Install and start Redis server |

## 📞 Need Help?

1. Check the full **SETUP_AND_STARTUP_GUIDE.md** for detailed instructions
2. Review logs in terminal output
3. Verify all prerequisites are met
4. Check the troubleshooting section in the main guide

---
**Quick Reference** | **Last Updated**: May 28, 2025
