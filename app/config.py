import os
from dotenv import load_dotenv

load_dotenv()

APISPEC_TITLE = "pricing-engine"
SERVICE_PREFIX = os.getenv('SERVICE_PREFIX', 'pricing')

SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'postgresql://localhost:5432/qna?sslmode=require'
SQLALCHEMY_TRACK_MODIFICATIONS = False

PERPLEXITY_API_URL = "https://api.perplexity.ai/chat/completions"
PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# API models
OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-4o')
PERPLEXITY_MODEL = os.getenv('PERPLEXITY_MODEL', 'sonar-pro')


AWS_REGION = os.getenv('AWS_REGION')
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')

EMAIL_SENDER = os.getenv('EMAIL_SENDER')
EMAIL_RECEIVER = os.getenv('EMAIL_RECEIVER')

# Data paths
HISTORICAL_DATA_PATH = os.getenv('HISTORICAL_DATA_PATH', 'data/historical_data.csv')

# Output file paths
RAW_OUTPUT_FILE = os.getenv('RAW_OUTPUT_FILE', 'data/raw_output.jsonl')
RESULTS_FILE = os.getenv('RESULTS_FILE', 'data/results.jsonl')

DB_CONFIG = {
    'host': os.environ.get('DB_HOST'),
    'database': os.environ.get('DB_NAME'),
    'user':os.environ.get('DB_USER'),
    'password':os.environ.get('DB_PASSWORD'),
    'port':os.environ.get('DB_PORT')
}

config = {
    'DB_CONFIG': DB_CONFIG
}

TARIFF_SERVICE_BASE_URL = os.getenv('TARIFF_SERVICE_BASE_URL', 'http://*************:5000')

# HTS2Duty Integration Settings
USE_LOCAL_HTS2DUTY = os.getenv('USE_LOCAL_HTS2DUTY', 'true').lower() == 'true'
HTS2DUTY_SERVICE_URL = os.getenv('HTS2DUTY_SERVICE_URL', 'http://localhost:8080')

# Export Data Handling Settings
SUPPRESS_EXPORT_DATA_FOR_NON_US = os.getenv('SUPPRESS_EXPORT_DATA_FOR_NON_US', 'true').lower() == 'true'
DEFAULT_EXPORT_DESTINATION = os.getenv('DEFAULT_EXPORT_DESTINATION', 'United States')
