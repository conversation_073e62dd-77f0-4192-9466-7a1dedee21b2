import datetime
import json
import logging
import re
from typing import Optional, <PERSON><PERSON>

from flask import request
from flask_restx import Namespace, Resource, fields, reqparse

from app import api
from app.services.trade_ranker_service import TradeRankerService
from app.services.trade_finder_service import TradeFinderService
from app.utils.formatters import format_number, format_currency, truncate_text

logger = logging.getLogger(__name__)

# Create the namespace for trade ranking APIs
ns = Namespace(name='trade-ranker', path="/v1/rank", description='Country and Supplier Ranking APIs')

# Response Models
metadata_model = api.model('Metadata', {
    'column_count': fields.Integer,
    'columns': fields.List(fields.String),
    'row_count': fields.Integer
})

result_model = api.model('Result', {
    'data': fields.Raw,
    'metadata': fields.Nested(metadata_model)
})







# ============================================================================
# INTEGRATED VALIDATION FUNCTIONS
# ============================================================================

def validate_hs_code(hs_code: str) -> Tuple[bool, Optional[str]]:
    """
    Validate HS code format.

    Args:
        hs_code: HS code to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not hs_code or not hs_code.strip():
        return False, "HS code is required"

    # Remove dots and check if it's a valid HS code format
    clean_hs_code = hs_code.replace(".", "")

    # HS codes are typically 6-10 digits
    if not clean_hs_code.isdigit() or len(clean_hs_code) < 4 or len(clean_hs_code) > 10:
        return False, "Invalid HS code format. Expected 4-10 digits."

    return True, None

def validate_country(country: str) -> Tuple[bool, Optional[str]]:
    """
    Validate country parameter.

    Args:
        country: Country name or code to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not country or not country.strip():
        return False, "Country is required"

    # Check for potentially dangerous inputs
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, country, re.IGNORECASE):
            return False, "Invalid country input detected"

    return True, None

def validate_months(months: int) -> Tuple[bool, Optional[str]]:
    """
    Validate months parameter.

    Args:
        months: Number of months to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not isinstance(months, int):
        return False, "Months must be an integer"

    if months < 1 or months > 60:
        return False, "Months must be between 1 and 60"

    return True, None

def sanitize_input(input_str: str) -> str:
    """
    Sanitize input string to prevent injection attacks.

    Args:
        input_str: Input string to sanitize

    Returns:
        Sanitized string
    """
    if not input_str:
        return ""

    # Remove potentially dangerous characters
    sanitized = re.sub(r'[;\'"]', '', input_str)

    # Limit length
    return sanitized[:500]

def format_ranking_data(data, data_type="country"):
    """
    Add formatted fields to ranking data for better display.

    Args:
        data: List of ranking data items
        data_type: Type of data ("country" or "supplier")

    Returns:
        List of formatted data items
    """
    formatted_data = []

    for item in data:
        formatted_item = item.copy()

        # Format common fields
        if 'average_fob' in item:
            formatted_item['formatted_average_fob'] = format_currency(item.get('average_fob', 0))

        if 'shipment_count' in item:
            formatted_item['formatted_shipment_count'] = format_number(item.get('shipment_count', 0), 0)

        if 'Total Duty' in item:
            formatted_item['formatted_total_duty'] = item.get('Total Duty', 'N/A')

        if 'Score' in item:
            formatted_item['formatted_score'] = format_number(item.get('Score', 0), 2)

        # Format country-specific fields
        if data_type == "country":
            if 'origin_country' in item:
                formatted_item['formatted_country'] = truncate_text(item.get('origin_country', ''), 30)

        # Format supplier-specific fields
        elif data_type == "supplier":
            if 'exporter_name' in item:
                formatted_item['formatted_exporter_name'] = truncate_text(item.get('exporter_name', ''), 50)

            if 'total_quantity' in item:
                formatted_item['formatted_total_quantity'] = format_number(item.get('total_quantity', 0), 2)

        formatted_data.append(formatted_item)

    return formatted_data

# Response Models
response_model = api.model('Response', {
    'query': fields.Raw,
    'result': fields.Nested(result_model),
    'success': fields.Boolean,
    'error': fields.String(required=False),
    'warning': fields.String(required=False)
})

@ns.route('/rank-suppliers')
class SupplierRanker(Resource):
    @ns.expect(api.model('SupplierRankRequest', {
        'query': fields.Raw(required=True),
        'result': fields.Nested(result_model)
    }))
    @ns.marshal_with(response_model)
    def post(self):
        """Rank suppliers based on composite scoring"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        # client_ip = request.remote_addr

        logger.info(f"Request received [ID: {request_id}]")# from IP: {client_ip}")

        try:
            request_data = request.get_json()

            if request_data and 'query' in request_data and 'result' in request_data and 'data' in request_data['result']:
                query_info = request_data.get('query', {})
                supplier_count = len(request_data['result']['data'])
                logger.info(f"Request [ID: {request_id}] - Query params: {json.dumps(query_info)} - Supplier count: {supplier_count}")
            else:
                logger.warning(f"Request [ID: {request_id}] - Invalid request format")
                ns.abort(400, "Invalid request format")

            suppliers = request_data['result']['data']

            if not suppliers:
                logger.error(f"Request [ID: {request_id}] - No suppliers provided")
                ns.abort(400, "No suppliers provided")

            top_suppliers = TradeRankerService().rank_suppliers(suppliers)

            columns = request_data['result']['metadata']['columns'].copy()
            if 'Rank' not in columns:
                columns.append('Rank')
            if 'Score' not in columns:
                columns.append('Score')

            response = {
                'query': request_data.get('query', {}),
                'result': {
                    'data': top_suppliers,
                    'metadata': {
                        'column_count': len(columns),
                        'columns': columns,
                        'row_count': len(top_suppliers)
                    }
                },
                'success': True
            }

            top_ranks = [f"{supplier.get('exporter_name', 'Unknown')} (Rank: {supplier.get('Rank')})"
                        for supplier in top_suppliers]
            logger.info(f"Request [ID: {request_id}] - Successful response with {len(top_suppliers)} suppliers: {', '.join(top_ranks)}")

            return response

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error processing request: {str(e)}", exc_info=True)
            ns.abort(500, str(e))

@ns.route('/rank-countries')
class CountryRanker(Resource):
    @ns.expect(api.model('CountryRankRequest', {
        'query': fields.Raw(required=True),
        'result': fields.Nested(result_model)
    }))
    @ns.marshal_with(response_model)
    def post(self):
        """Rank countries based on FOB, shipment count, and tariff data"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        # client_ip = request.remote_addr

        logger.info(f"Country ranking request received [ID: {request_id}]")# from IP: {client_ip}")

        try:
            request_data = request.get_json()
            logger.info(f"Request data: {request_data}")

            if not request_data or 'result' not in request_data or 'data' not in request_data['result']:
                logger.error(f"Request [ID: {request_id}] - Invalid request format")
                ns.abort(400, "Invalid request format")

            query_params = request_data.get('query', {})
            hs_code = query_params.get('hs_code', '')

            if not hs_code:
                logger.error(f"Request [ID: {request_id}] - Missing HS code in query parameters")
                ns.abort(400, "HS code is required in query parameters")

            # Validate HS code
            is_valid, error_message = validate_hs_code(hs_code)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - HS code validation failed: {error_message}")
                ns.abort(400, error_message)

            # Sanitize HS code
            hs_code = sanitize_input(hs_code)

            countries = request_data['result']['data']

            if not countries:
                logger.error(f"Request [ID: {request_id}] - No countries provided")
                ns.abort(400, "No countries provided")

            logger.info(f"Request [ID: {request_id}] - Processing {len(countries)} countries")

            top_countries = TradeRankerService().rank_countries(countries, hs_code)

            # Apply formatting to the ranking data
            formatted_countries = format_ranking_data(top_countries, "country")

            columns = [
                'origin_country',
                'average_fob',
                'shipment_count',
                'Total Duty',
                'Rank',
                'Score'
            ]

            response = {
                'query': query_params,
                'result': {
                    'data': formatted_countries,
                    'metadata': {
                        'column_count': len(columns),
                        'columns': columns,
                        'row_count': len(formatted_countries)
                    }
                },
                'success': True
            }

            top_ranks = [f"{country.get('origin_country', 'Unknown')} (Rank: {country.get('Rank')})"
                        for country in top_countries]
            logger.info(f"Request [ID: {request_id}] - Successful response with {len(top_countries)} countries: {', '.join(top_ranks)}")

            return response

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error processing request: {str(e)}", exc_info=True)
            ns.abort(500, str(e))

# Add new query parameters for the combined endpoint
ranked_geographies_query_params = reqparse.RequestParser()
ranked_geographies_query_params.add_argument('hs_code', type=str, required=True, help='HS code is required')
ranked_geographies_query_params.add_argument('months', type=int, default=12, help='Last k months to consider')
ranked_geographies_query_params.add_argument('destination', type=str, default='United States', help='Destination country')
ranked_geographies_query_params.add_argument('chemical_name', type=str, required=False, help='Name of the chemical')

@ns.route('/ranked-geographies')
class RankedGeographies(Resource):
    @ns.expect(ranked_geographies_query_params)
    @ns.marshal_with(response_model)
    def get(self):
        """Get top geographies supplying products and rank them"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /ranked-geographies")

        try:
            args = ranked_geographies_query_params.parse_args()
            logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")

            # Validate inputs
            hs_code = args.get('hs_code', '')
            destination = args.get('destination', 'United States')
            months = args.get('months', 12)
            chemical_name = args.get('chemical_name', '')

            # Validate HS code
            is_valid, error_message = validate_hs_code(hs_code)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - HS code validation failed: {error_message}")
                return {
                    'success': False,
                    'error': error_message
                }, 400

            # Validate destination country
            is_valid, error_message = validate_country(destination)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - Destination validation failed: {error_message}")
                return {
                    'success': False,
                    'error': error_message
                }, 400

            # Validate months
            is_valid, error_message = validate_months(months)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - Months validation failed: {error_message}")
                return {
                    'success': False,
                    'error': error_message
                }, 400

            # Sanitize inputs
            hs_code = sanitize_input(hs_code)
            destination = sanitize_input(destination)
            chemical_name = sanitize_input(chemical_name)

            # Step 1: Get top suppliers by geography
            countries_data = TradeFinderService().get_top_suppliers_by_geography(
                hs_code,
                months,
                destination,
                chemical_name
            )

            if not countries_data or 'data' not in countries_data:
                logger.error(f"Request [ID: {request_id}] - No countries data found")
                ns.abort(400, "No countries data found")

            # Step 2: Rank the countries
            top_countries = TradeRankerService().rank_countries(countries_data['data'], args['hs_code'])

            # Apply formatting to the ranking data
            formatted_countries = format_ranking_data(top_countries, "country")

            columns = [
                'origin_country',
                'average_fob',
                'shipment_count',
                'Total Duty',
                'Rank',
                'Score'
            ]

            # Check if chemical name filtering was used and if fallback occurred
            fallback_used = False
            if chemical_name and countries_data.get('fallback_used'):
                fallback_used = True

            response = {
                'query': args,
                'result': {
                    'data': formatted_countries,
                    'metadata': {
                        'column_count': len(columns),
                        'columns': columns,
                        'row_count': len(formatted_countries),
                        'chemical_name_fallback_used': fallback_used
                    }
                },
                'success': True
            }

            if fallback_used:
                response['warning'] = f"No results found with chemical name '{chemical_name}'. Results shown are for all products under HS code {hs_code}."

            top_ranks = [f"{country.get('origin_country', 'Unknown')} (Rank: {country.get('Rank')})"
                        for country in top_countries]
            logger.info(f"Request [ID: {request_id}] - Successful response with {len(top_countries)} countries: {', '.join(top_ranks)}")

            return response

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error processing request: {str(e)}", exc_info=True)
            ns.abort(500, str(e))

# Add new query parameters for the combined suppliers endpoint
ranked_suppliers_query_params = reqparse.RequestParser()
ranked_suppliers_query_params.add_argument('hs_code', type=str, required=True, help='HS code is required')
ranked_suppliers_query_params.add_argument('origin', type=str, required=True, help='Origin country')
ranked_suppliers_query_params.add_argument('destination', type=str, default='United States', help='Destination country')
ranked_suppliers_query_params.add_argument('months', type=int, default=12, help='Last k months to consider')

@ns.route('/ranked-suppliers')
class RankedSuppliers(Resource):
    @ns.expect(ranked_suppliers_query_params)
    @ns.marshal_with(response_model)
    def get(self):
        """Get top suppliers for a specific HS code, origin, and destination and rank them"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /ranked-suppliers")

        try:
            args = ranked_suppliers_query_params.parse_args()
            logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")

            # Validate inputs
            hs_code = args.get('hs_code', '')
            origin = args.get('origin', '')
            destination = args.get('destination', 'United States')
            months = args.get('months', 12)

            # Validate HS code
            is_valid, error_message = validate_hs_code(hs_code)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - HS code validation failed: {error_message}")
                return {
                    'success': False,
                    'error': error_message
                }, 400

            # Validate origin country
            is_valid, error_message = validate_country(origin)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - Origin validation failed: {error_message}")
                return {
                    'success': False,
                    'error': error_message
                }, 400

            # Validate destination country
            is_valid, error_message = validate_country(destination)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - Destination validation failed: {error_message}")
                return {
                    'success': False,
                    'error': error_message
                }, 400

            # Validate months
            is_valid, error_message = validate_months(months)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - Months validation failed: {error_message}")
                return {
                    'success': False,
                    'error': error_message
                }, 400

            # Sanitize inputs
            hs_code = sanitize_input(hs_code)
            origin = sanitize_input(origin)
            destination = sanitize_input(destination)

            # Step 1: Get top suppliers
            suppliers_data = TradeFinderService().get_top_suppliers(
                hs_code,
                origin,
                destination,
                months
            )

            if not suppliers_data or 'data' not in suppliers_data:
                logger.error(f"Request [ID: {request_id}] - No suppliers data found")
                ns.abort(400, "No suppliers data found")

            # Step 2: Rank the suppliers
            top_suppliers = TradeRankerService().rank_suppliers(suppliers_data['data'])

            # Apply formatting to the ranking data
            formatted_suppliers = format_ranking_data(top_suppliers, "supplier")

            columns = suppliers_data['metadata']['columns'].copy()
            if 'Rank' not in columns:
                columns.append('Rank')
            if 'Score' not in columns:
                columns.append('Score')

            response = {
                'query': args,
                'result': {
                    'data': formatted_suppliers,
                    'metadata': {
                        'column_count': len(columns),
                        'columns': columns,
                        'row_count': len(formatted_suppliers)
                    }
                },
                'success': True
            }

            top_ranks = [f"{supplier.get('exporter_name', 'Unknown')} (Rank: {supplier.get('Rank')})"
                        for supplier in top_suppliers]
            logger.info(f"Request [ID: {request_id}] - Successful response with {len(top_suppliers)} suppliers: {', '.join(top_ranks)}")

            return response

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error processing request: {str(e)}", exc_info=True)
            ns.abort(500, str(e))

api.add_namespace(ns)


