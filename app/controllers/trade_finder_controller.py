import datetime
import json
import logging
import re
import requests
import psycopg2
import time
from typing import Dict, Any, Optional, List, Tuple
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

from flask import request, jsonify
from flask_restx import Namespace, Resource, fields, reqparse

from app import api
from app.config import DB_CONFIG
from app.services.trade_finder_service import TradeFinderService
from app.services.trade_ranker_service import TradeRankerService
from app.services.openai_client import OpenAIClient
from app.services.perplexity_client import PerplexityClient
from app.models.country import CountryLookupResult
from app.models.supplier import SupplierLookupResult
from app.utils.formatters import format_number, format_currency, format_percentage, format_weight, truncate_text, format_list_display

logger = logging.getLogger(__name__)


# ============================================================================
# WEB SCRAPER FOR CONTACT INFORMATION
# ============================================================================

class WebScraper:
    """
    Advanced web scraper for extracting contact information from company websites.
    This provides true web scraping capabilities beyond what procure intelligence offers.
    """

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # Regex patterns for contact information extraction
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_pattern = re.compile(r'(\+?[\d\s\-\(\)]{10,20})')
        self.website_pattern = re.compile(r'https?://[^\s<>"]+')

    def scrape_company_website(self, company_name: str, search_terms: List[str] = None) -> Dict[str, str]:
        """
        Scrape company website for contact information.

        Args:
            company_name: Name of the company to search for
            search_terms: Additional search terms to help find the company

        Returns:
            Dictionary with extracted contact information
        """
        contact_info = {
            "website": "",
            "email": "",
            "phone": "",
            "address": ""
        }

        try:
            # First, try to find the company website using search
            website_url = self._find_company_website(company_name, search_terms)

            if website_url:
                contact_info["website"] = website_url

                # Scrape the website for contact information
                scraped_contact = self._scrape_website_contact(website_url)
                contact_info.update(scraped_contact)

        except Exception as e:
            logger.error(f"Error scraping website for {company_name}: {str(e)}")

        return contact_info

    def _find_company_website(self, company_name: str, search_terms: List[str] = None) -> str:
        """
        Find company website using enhanced search and domain validation.

        Args:
            company_name: Name of the company
            search_terms: Additional search terms

        Returns:
            Company website URL or empty string if not found
        """
        try:
            logger.info(f"Searching for website of company: {company_name}")

            # Try common website patterns first
            potential_domains = self._generate_potential_domains(company_name)
            logger.debug(f"Generated {len(potential_domains)} potential domains for {company_name}")

            for domain in potential_domains:
                logger.debug(f"Checking domain: {domain}")
                if self._check_website_exists(domain):
                    logger.info(f"Found working website for {company_name}: {domain}")
                    return domain

            # If domain guessing fails, try enhanced search using Perplexity
            website_url = self._search_company_website_with_llm(company_name, search_terms)
            if website_url:
                logger.info(f"Found website via LLM search for {company_name}: {website_url}")
                return website_url

            logger.warning(f"No website found for {company_name}")
            return ""

        except Exception as e:
            logger.error(f"Error finding website for {company_name}: {str(e)}")
            return ""

    def _generate_potential_domains(self, company_name: str) -> List[str]:
        """
        Generate potential domain names for a company.

        Args:
            company_name: Name of the company

        Returns:
            List of potential domain URLs
        """
        # Clean company name for domain generation
        clean_name = re.sub(r'[^a-zA-Z0-9\s]', '', company_name.lower())
        clean_name = re.sub(r'\s+', '', clean_name)

        # Remove common company suffixes
        suffixes_to_remove = ['ltd', 'limited', 'inc', 'incorporated', 'corp', 'corporation', 'co', 'company', 'llc', 'plc']
        for suffix in suffixes_to_remove:
            if clean_name.endswith(suffix):
                clean_name = clean_name[:-len(suffix)]

        # Generate potential domains
        domains = [
            f"https://www.{clean_name}.com",
            f"https://{clean_name}.com",
            f"https://www.{clean_name}.net",
            f"https://www.{clean_name}.org",
            f"https://www.{clean_name}.co.uk",
            f"https://www.{clean_name}.de",
            f"https://www.{clean_name}.cn",
            f"https://www.{clean_name}.in"
        ]

        return domains

    def _search_company_website_with_llm(self, company_name: str, search_terms: List[str] = None) -> str:
        """
        Use LLM to search for company website when domain guessing fails.

        Args:
            company_name: Name of the company
            search_terms: Additional search terms

        Returns:
            Company website URL or empty string if not found
        """
        try:
            # Import here to avoid circular imports
            from app.services.perplexity_client import PerplexityClient

            search_query = f"official website URL for {company_name}"
            if search_terms:
                search_query += f" {' '.join(search_terms)}"

            prompt = f"""Find the official website URL for the company "{company_name}".

            Search for the company's official website and return ONLY the URL in this exact format:
            {{
                "website": "https://example.com"
            }}

            If no official website can be found, return:
            {{
                "website": ""
            }}

            Do not include any explanations or additional text."""

            payload = {
                "model": "sonar-pro",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a web search specialist. Return only valid JSON with the website URL."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1
            }

            perplexity_client = PerplexityClient()
            response = perplexity_client.query(payload)

            if response and "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"].strip()

                # Clean up response
                if content.startswith("```json"):
                    content = content[7:]
                if content.endswith("```"):
                    content = content[:-3]
                content = content.strip()

                try:
                    result = json.loads(content)
                    website_url = result.get("website", "")

                    if website_url and self._check_website_exists(website_url):
                        return website_url

                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse LLM website search response for {company_name}")

        except Exception as e:
            logger.error(f"Error in LLM website search for {company_name}: {str(e)}")

        return ""

    def _check_website_exists(self, url: str) -> bool:
        """
        Check if a website exists and is accessible.

        Args:
            url: URL to check

        Returns:
            True if website exists and is accessible
        """
        try:
            response = self.session.head(url, timeout=5, allow_redirects=True)
            return response.status_code == 200
        except:
            return False

    def _scrape_website_contact(self, website_url: str) -> Dict[str, str]:
        """
        Comprehensively scrape contact information from a website and its contact pages.

        Args:
            website_url: URL of the website to scrape

        Returns:
            Dictionary with extracted contact information
        """
        contact_info = {
            "email": "",
            "phone": "",
            "address": ""
        }

        try:
            logger.info(f"Starting comprehensive contact extraction from: {website_url}")

            # Get the main page
            response = self.session.get(website_url, timeout=15)
            if response.status_code != 200:
                logger.warning(f"Failed to access main page: {response.status_code}")
                return contact_info

            soup = BeautifulSoup(response.content, 'html.parser')
            logger.info(f"Successfully loaded main page, content length: {len(response.content)}")

            # Extract contact information from main page
            main_contact = self._extract_contact_from_page(soup, website_url)
            contact_info.update(main_contact)
            logger.info(f"Main page extraction results: {main_contact}")

            # Always try to find and scrape contact pages for better results
            contact_pages = self._find_contact_pages(soup, website_url)
            logger.info(f"Found {len(contact_pages)} potential contact pages: {contact_pages}")

            # Scrape ALL contact pages (not just 2) for maximum coverage
            for i, contact_url in enumerate(contact_pages[:5]):  # Increased to 5 pages
                try:
                    logger.info(f"Scraping contact page {i+1}: {contact_url}")
                    contact_response = self.session.get(contact_url, timeout=15)

                    if contact_response.status_code == 200:
                        contact_soup = BeautifulSoup(contact_response.content, 'html.parser')
                        page_contact = self._extract_contact_from_page(contact_soup, contact_url)
                        logger.info(f"Contact page {i+1} extraction results: {page_contact}")

                        # Update with any new information found (prioritize non-empty values)
                        for key, value in page_contact.items():
                            if value and value.strip():
                                if not contact_info[key] or len(value) > len(contact_info[key]):
                                    # Use longer/better contact info if found
                                    old_value = contact_info[key]
                                    contact_info[key] = value
                                    logger.info(f"Updated {key}: '{old_value}' -> '{value}'")

                    else:
                        logger.warning(f"Failed to access contact page {contact_url}: {contact_response.status_code}")

                except Exception as e:
                    logger.warning(f"Error scraping contact page {contact_url}: {str(e)}")
                    continue

            # Try additional aggressive extraction methods if still missing info
            missing_fields = [k for k, v in contact_info.items() if not v]
            if missing_fields:
                logger.info(f"Still missing fields {missing_fields}, trying aggressive extraction...")
                aggressive_contact = self._aggressive_contact_extraction(soup, website_url)

                for key, value in aggressive_contact.items():
                    if value and not contact_info[key]:
                        contact_info[key] = value
                        logger.info(f"Aggressive extraction found {key}: {value}")

            logger.info(f"Final contact extraction results: {contact_info}")

        except Exception as e:
            logger.error(f"Error scraping website {website_url}: {str(e)}")

        return contact_info

    def _extract_contact_from_page(self, soup: BeautifulSoup, base_url: str) -> Dict[str, str]:
        """
        Extract contact information from a BeautifulSoup page object with enhanced patterns.

        Args:
            soup: BeautifulSoup object of the page
            base_url: Base URL for resolving relative links

        Returns:
            Dictionary with extracted contact information
        """
        contact_info = {
            "email": "",
            "phone": "",
            "address": ""
        }

        # Try structured data extraction first (JSON-LD, microdata)
        structured_contact = self._extract_structured_contact(soup)
        if structured_contact:
            contact_info.update(structured_contact)

        # Get all text content for pattern matching
        page_text = soup.get_text()

        # Enhanced email extraction with multiple techniques
        if not contact_info["email"]:
            # Try multiple email extraction methods
            all_emails = set()

            # Method 1: Standard regex
            emails = self.email_pattern.findall(page_text)
            all_emails.update(emails)

            # Method 2: Look for emails in specific elements
            email_elements = soup.find_all(['a', 'span', 'div', 'p'], href=re.compile(r'mailto:', re.I))
            for elem in email_elements:
                href = elem.get('href', '')
                if href.startswith('mailto:'):
                    email = href.replace('mailto:', '').split('?')[0]
                    all_emails.add(email)

            # Method 3: Look for emails in text content of contact-related elements
            contact_elements = soup.find_all(['div', 'span', 'p'], class_=re.compile(r'email|contact|mail', re.I))
            for elem in contact_elements:
                elem_emails = self.email_pattern.findall(elem.get_text())
                all_emails.update(elem_emails)

            if all_emails:
                # Filter and prioritize emails
                business_emails = []
                other_emails = []

                for email in all_emails:
                    email_lower = email.lower()
                    if not any(skip in email_lower for skip in [
                        'noreply', 'no-reply', 'donotreply', 'support@example',
                        'example@', 'test@', 'admin@', 'webmaster@', 'privacy@',
                        'legal@', 'abuse@', 'postmaster@', 'security@', 'infosecurity',
                        'alerts@', 'notification@', 'system@'
                    ]):
                        # Prioritize business emails
                        if any(prefix in email_lower for prefix in [
                            'info@', 'contact@', 'sales@', 'inquiry@', 'enquiry@',
                            'business@', 'office@', 'hello@', 'mail@'
                        ]):
                            business_emails.append(email)
                        else:
                            other_emails.append(email)

                if business_emails:
                    contact_info["email"] = business_emails[0]
                elif other_emails:
                    contact_info["email"] = other_emails[0]

        # Enhanced phone extraction with multiple techniques
        if not contact_info["phone"]:
            all_phones = set()

            # Method 1: Multiple phone patterns
            phone_patterns = [
                r'(?:phone|tel|call|mobile|cell)[\s:]*(\+?[\d\s\-\(\)]{8,20})',  # With prefix
                r'(\+\d{1,3}[\s\-]?\(?\d{3,4}\)?[\s\-]?\d{3,4}[\s\-]?\d{3,6})',  # International
                r'(\(\d{3,4}\)[\s\-]?\d{3,4}[\s\-]?\d{3,6})',  # Area code in parentheses
                r'(\d{3,4}[\s\-]\d{3,4}[\s\-]\d{3,6})',  # Simple format
                r'(\+?[\d\s\-\(\)]{10,20})',  # General pattern
            ]

            for pattern in phone_patterns:
                phones = re.findall(pattern, page_text, re.IGNORECASE)
                all_phones.update(phones)

            # Method 2: Look for phones in tel: links
            tel_links = soup.find_all('a', href=re.compile(r'tel:', re.I))
            for link in tel_links:
                href = link.get('href', '')
                if href.startswith('tel:'):
                    phone = href.replace('tel:', '').strip()
                    all_phones.add(phone)

            # Method 3: Look in phone/contact specific elements
            phone_elements = soup.find_all(['div', 'span', 'p'], class_=re.compile(r'phone|tel|contact', re.I))
            for elem in phone_elements:
                for pattern in phone_patterns:
                    phones = re.findall(pattern, elem.get_text(), re.IGNORECASE)
                    all_phones.update(phones)

            # Clean and validate phone numbers
            for phone in all_phones:
                # Clean the phone number more aggressively
                cleaned_phone = re.sub(r'[^\d\+\-\(\)\s]', '', phone)
                # Remove excessive whitespace and newlines
                cleaned_phone = re.sub(r'\s+', ' ', cleaned_phone.strip())
                # Remove trailing dashes or incomplete numbers
                cleaned_phone = re.sub(r'[\-\s]+$', '', cleaned_phone)

                digit_count = len(re.sub(r'[^\d]', '', cleaned_phone))
                # More strict validation for phone numbers
                if 7 <= digit_count <= 15 and len(cleaned_phone.strip()) >= 7:
                    # Additional validation: should not be mostly non-digits
                    if digit_count / len(cleaned_phone.replace(' ', '')) > 0.6:
                        contact_info["phone"] = cleaned_phone.strip()
                        break

        # Enhanced address extraction
        if not contact_info["address"]:
            address = self._extract_address(soup)
            if address:
                contact_info["address"] = address

        return contact_info

    def _find_contact_pages(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """
        Comprehensively find contact and about page URLs from the main page.

        Args:
            soup: BeautifulSoup object of the main page
            base_url: Base URL for resolving relative links

        Returns:
            List of contact page URLs prioritized by relevance
        """
        contact_pages = []

        # Expanded contact keywords with priority scoring
        priority_keywords = {
            'contact': 10,
            'contact-us': 10,
            'contact_us': 10,
            'contactus': 10,
            'get-in-touch': 9,
            'reach-us': 9,
            'reach': 8,
            'about': 7,
            'about-us': 7,
            'about_us': 7,
            'location': 6,
            'office': 6,
            'offices': 6,
            'address': 6,
            'headquarters': 6,
            'company': 5,
            'info': 5,
            'information': 5,
            'support': 4,
            'help': 4,
            'inquiry': 6,
            'enquiry': 6,
            'sales': 5,
            'business': 4
        }

        found_links = []

        # Look for links with contact-related text in href and link text
        for link in soup.find_all('a', href=True):
            href = link['href'].lower().strip()
            text = link.get_text().lower().strip()

            # Skip empty links, javascript, mailto, tel links
            if not href or href.startswith(('javascript:', 'mailto:', 'tel:', '#')):
                continue

            max_score = 0
            matched_keyword = ""

            # Check both href and text for keywords
            for keyword, score in priority_keywords.items():
                if keyword in href or keyword in text:
                    if score > max_score:
                        max_score = score
                        matched_keyword = keyword

            if max_score > 0:
                full_url = urljoin(base_url, link['href'])
                # Avoid duplicates and self-references
                if full_url != base_url and full_url not in [item[0] for item in found_links]:
                    found_links.append((full_url, max_score, matched_keyword))

        # Sort by priority score (highest first)
        found_links.sort(key=lambda x: x[1], reverse=True)

        # Extract just the URLs
        contact_pages = [url for url, _, _ in found_links]

        # Also try common contact page patterns if not found
        if not contact_pages:
            common_patterns = [
                '/contact',
                '/contact-us',
                '/contact_us',
                '/contactus',
                '/about',
                '/about-us',
                '/about_us',
                '/company',
                '/reach-us',
                '/get-in-touch',
                '/office',
                '/location',
                '/headquarters'
            ]

            for pattern in common_patterns:
                potential_url = urljoin(base_url, pattern)
                if potential_url not in contact_pages:
                    contact_pages.append(potential_url)

        logger.info(f"Found contact pages with priorities: {[(url, score, kw) for url, score, kw in found_links[:5]]}")
        return contact_pages

    def _extract_structured_contact(self, soup: BeautifulSoup) -> Dict[str, str]:
        """
        Extract contact information from structured data (JSON-LD, microdata).

        Args:
            soup: BeautifulSoup object of the page

        Returns:
            Dictionary with extracted structured contact information
        """
        contact_info = {}

        try:
            # Try JSON-LD structured data
            json_ld_scripts = soup.find_all('script', type='application/ld+json')
            for script in json_ld_scripts:
                try:
                    data = json.loads(script.string)
                    if isinstance(data, dict):
                        # Look for Organization or LocalBusiness schema
                        if data.get('@type') in ['Organization', 'LocalBusiness', 'Corporation']:
                            if 'email' in data and not contact_info.get('email'):
                                contact_info['email'] = data['email']
                            if 'telephone' in data and not contact_info.get('phone'):
                                contact_info['phone'] = data['telephone']
                            if 'address' in data and not contact_info.get('address'):
                                addr = data['address']
                                if isinstance(addr, dict):
                                    # Construct address from structured data
                                    address_parts = []
                                    for key in ['streetAddress', 'addressLocality', 'addressRegion', 'postalCode', 'addressCountry']:
                                        if key in addr and addr[key]:
                                            address_parts.append(str(addr[key]))
                                    if address_parts:
                                        contact_info['address'] = ', '.join(address_parts)
                                elif isinstance(addr, str):
                                    contact_info['address'] = addr
                except (json.JSONDecodeError, KeyError):
                    continue

            # Try microdata
            if not contact_info:
                # Look for itemtype="http://schema.org/Organization" or similar
                org_elements = soup.find_all(attrs={"itemtype": re.compile(r"schema\.org/(Organization|LocalBusiness|Corporation)")})
                for org_elem in org_elements:
                    if not contact_info.get('email'):
                        email_elem = org_elem.find(attrs={"itemprop": "email"})
                        if email_elem:
                            contact_info['email'] = email_elem.get_text().strip()

                    if not contact_info.get('phone'):
                        phone_elem = org_elem.find(attrs={"itemprop": "telephone"})
                        if phone_elem:
                            contact_info['phone'] = phone_elem.get_text().strip()

                    if not contact_info.get('address'):
                        address_elem = org_elem.find(attrs={"itemprop": "address"})
                        if address_elem:
                            contact_info['address'] = address_elem.get_text().strip()

        except Exception as e:
            logger.debug(f"Error extracting structured contact data: {str(e)}")

        return contact_info

    def _extract_address(self, soup: BeautifulSoup) -> str:
        """
        Extract address information from a page with enhanced patterns.

        Args:
            soup: BeautifulSoup object of the page

        Returns:
            Extracted address string
        """
        # Look for address in structured data and common selectors
        address_selectors = [
            '[itemtype*="PostalAddress"]',
            '[itemprop="address"]',
            '.address',
            '.location',
            '.contact-address',
            '.company-address',
            '#address',
            '.office-address',
            '.headquarters'
        ]

        for selector in address_selectors:
            address_elem = soup.select_one(selector)
            if address_elem:
                address_text = address_elem.get_text().strip()
                if len(address_text) > 10:  # Basic validation
                    return address_text

        # Try to find address patterns in text
        page_text = soup.get_text()

        # Look for common address patterns
        address_patterns = [
            r'(?:address|location|office)[\s:]*([^\n]{20,100}(?:street|road|avenue|lane|drive|blvd|plaza|square)[^\n]{0,50})',
            r'(\d+[^\n]*(?:street|road|avenue|lane|drive|blvd|plaza|square)[^\n]{0,50})',
        ]

        for pattern in address_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                # Return the first reasonable match
                for match in matches:
                    if len(match.strip()) > 15:
                        return match.strip()

        return ""

    def _aggressive_contact_extraction(self, soup: BeautifulSoup, base_url: str) -> Dict[str, str]:
        """
        Aggressive contact extraction using multiple techniques when standard methods fail.

        Args:
            soup: BeautifulSoup object of the page
            base_url: Base URL for context

        Returns:
            Dictionary with extracted contact information
        """
        contact_info = {
            "email": "",
            "phone": "",
            "address": ""
        }

        try:
            page_text = soup.get_text()

            # Aggressive email extraction with more patterns
            if not contact_info["email"]:
                email_patterns = [
                    r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Standard
                    r'(?:email|e-mail|mail)[\s:]*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',  # With prefix
                    r'([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})(?:\s|$)',  # Word boundary
                ]

                all_emails = []
                for pattern in email_patterns:
                    emails = re.findall(pattern, page_text, re.IGNORECASE)
                    all_emails.extend(emails)

                # Filter and prioritize emails
                business_emails = []
                other_emails = []

                for email in all_emails:
                    email_lower = email.lower()
                    if not any(skip in email_lower for skip in [
                        'noreply', 'no-reply', 'donotreply', 'example@', 'test@',
                        'admin@', 'webmaster@', 'support@example', 'privacy@',
                        'legal@', 'abuse@', 'postmaster@'
                    ]):
                        # Prioritize business emails
                        if any(prefix in email_lower for prefix in [
                            'info@', 'contact@', 'sales@', 'inquiry@', 'enquiry@',
                            'business@', 'office@', 'hello@', 'mail@'
                        ]):
                            business_emails.append(email)
                        else:
                            other_emails.append(email)

                if business_emails:
                    contact_info["email"] = business_emails[0]
                elif other_emails:
                    contact_info["email"] = other_emails[0]

            # Aggressive phone extraction with international patterns
            if not contact_info["phone"]:
                phone_patterns = [
                    r'(?:phone|tel|call|mobile|cell)[\s:]*(\+?[\d\s\-\(\)]{10,20})',  # With prefix
                    r'(\+\d{1,3}[\s\-]?\(?\d{3,4}\)?[\s\-]?\d{3,4}[\s\-]?\d{3,6})',  # International
                    r'(\(\d{3,4}\)[\s\-]?\d{3,4}[\s\-]?\d{3,6})',  # Area code in parentheses
                    r'(\d{3,4}[\s\-]\d{3,4}[\s\-]\d{3,6})',  # Simple format
                    r'(\+?[\d\s\-\(\)]{10,20})',  # General pattern
                ]

                for pattern in phone_patterns:
                    phones = re.findall(pattern, page_text, re.IGNORECASE)
                    for phone in phones:
                        cleaned_phone = re.sub(r'[^\d\+\-\(\)\s]', '', phone)
                        digit_count = len(re.sub(r'[^\d]', '', cleaned_phone))
                        if 7 <= digit_count <= 15:  # Valid phone number length
                            contact_info["phone"] = cleaned_phone.strip()
                            break
                    if contact_info["phone"]:
                        break

            # Aggressive address extraction
            if not contact_info["address"]:
                address_patterns = [
                    r'(?:address|location|office|headquarters)[\s:]*([^\n]{20,150}(?:street|road|avenue|lane|drive|blvd|plaza|square|way|court|place)[^\n]{0,50})',
                    r'(\d+[^\n]*(?:street|road|avenue|lane|drive|blvd|plaza|square|way|court|place)[^\n]{0,100})',
                    r'(?:located at|find us at|visit us at)[\s:]*([^\n]{15,100})',
                    r'(\d+[^\n]*(?:floor|suite|unit|building)[^\n]{0,80})',
                ]

                for pattern in address_patterns:
                    matches = re.findall(pattern, page_text, re.IGNORECASE)
                    for match in matches:
                        if len(match.strip()) > 20:  # Reasonable address length
                            contact_info["address"] = match.strip()
                            break
                    if contact_info["address"]:
                        break

            # Try to extract from footer or specific sections
            if not all(contact_info.values()):
                footer = soup.find('footer')
                if footer:
                    footer_contact = self._extract_contact_from_page(footer, base_url)
                    for key, value in footer_contact.items():
                        if value and not contact_info[key]:
                            contact_info[key] = value

            # Try contact sections
            contact_sections = soup.find_all(['div', 'section'], class_=re.compile(r'contact|address|phone|email', re.I))
            for section in contact_sections:
                section_contact = self._extract_contact_from_page(section, base_url)
                for key, value in section_contact.items():
                    if value and not contact_info[key]:
                        contact_info[key] = value

        except Exception as e:
            logger.debug(f"Error in aggressive contact extraction: {str(e)}")

        return contact_info


# ============================================================================
# INTEGRATED DATABASE CLIENT
# ============================================================================

class DatabaseClient:
    """
    Enhanced database client with error handling and connection management.
    """

    def __init__(self, db_config: Dict[str, Any]):
        """
        Initialize the database client.

        Args:
            db_config: Database configuration dictionary
        """
        self.db_config = db_config

    def get_connection(self):
        """
        Get a database connection.

        Returns:
            Database connection object
        """
        try:
            logger.debug(f"Connecting to database with config: {self.db_config}")
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            logger.error(f"Error connecting to database: {e}")
            raise

    def execute_query(self, query: str, params: tuple = None) -> Dict[str, Any]:
        """
        Execute a query against the database with enhanced error handling.

        Args:
            query: SQL query to execute
            params: Parameters for the query

        Returns:
            Dictionary with query results and metadata
        """
        logger.debug(f"Executing query: {query} with params: {params}")
        connection = None
        cursor = None

        try:
            # Get a connection
            connection = self.get_connection()
            cursor = connection.cursor()

            # Execute query with timing
            start_time = time.time()
            cursor.execute(query, params)
            query_time = time.time() - start_time

            # Get column names and results
            column_names = [desc[0] for desc in cursor.description] if cursor.description else []
            results = cursor.fetchall() if cursor.description else []

            # Format results as a list of dictionaries
            formatted_results = []
            for row in results:
                formatted_results.append(dict(zip(column_names, row)))

            return {
                "status": "success",
                "metadata": {
                    "row_count": len(formatted_results),
                    "column_count": len(column_names),
                    "columns": column_names,
                    "query_time_ms": round(query_time * 1000, 2)
                },
                "data": formatted_results
            }

        except Exception as e:
            logger.error(f"Database error: {e}")
            if connection:
                connection.rollback()
            return {
                "status": "error",
                "message": f"Database error: {str(e)}",
                "data": []
            }

        finally:
            # Close resources
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def test_connection(self) -> bool:
        """
        Test the database connection.

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            connection = self.get_connection()
            connection.close()
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False

# ============================================================================
# INTEGRATED VALIDATION FUNCTIONS
# ============================================================================

def validate_hs_code(hs_code: str) -> Tuple[bool, Optional[str]]:
    """
    Validate HS code format.

    Args:
        hs_code: HS code to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not hs_code or not hs_code.strip():
        return False, "HS code is required"

    # Remove dots and check if it's a valid HS code format
    clean_hs_code = hs_code.replace(".", "")

    # HS codes are typically 6-10 digits
    if not clean_hs_code.isdigit() or len(clean_hs_code) < 4 or len(clean_hs_code) > 10:
        return False, "Invalid HS code format. Expected 4-10 digits."

    return True, None

def validate_country(country: str) -> Tuple[bool, Optional[str]]:
    """
    Validate country parameter.

    Args:
        country: Country name or code to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not country or not country.strip():
        return False, "Country is required"

    # Check for potentially dangerous inputs
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, country, re.IGNORECASE):
            return False, "Invalid country input detected"

    return True, None

def validate_months(months: int) -> Tuple[bool, Optional[str]]:
    """
    Validate months parameter.

    Args:
        months: Number of months to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not isinstance(months, int):
        return False, "Months must be an integer"

    if months < 1 or months > 60:
        return False, "Months must be between 1 and 60"

    return True, None

def validate_supplier_name(supplier_name: str) -> Tuple[bool, Optional[str]]:
    """
    Validate supplier name parameter.

    Args:
        supplier_name: Supplier name to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not supplier_name or not supplier_name.strip():
        return False, "Supplier name is required"

    # Check for potentially dangerous inputs
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, supplier_name, re.IGNORECASE):
            return False, "Invalid supplier name input detected"

    return True, None

def validate_chemical_input(chemical_name: str, application: str) -> Tuple[bool, Optional[str]]:
    """
    Validate chemical input parameters.

    Args:
        chemical_name: Name of the chemical
        application: Application of the chemical

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not chemical_name or not chemical_name.strip():
        return False, "Chemical name is required"

    if not application or not application.strip():
        return False, "Chemical application is required"

    # Check for potentially dangerous inputs (SQL injection, etc.)
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, chemical_name, re.IGNORECASE) or re.search(pattern, application, re.IGNORECASE):
            return False, "Invalid input detected"

    return True, None

def sanitize_input(input_str: str) -> str:
    """
    Sanitize input string to prevent injection attacks.

    Args:
        input_str: Input string to sanitize

    Returns:
        Sanitized string
    """
    if not input_str:
        return ""

    # Remove potentially dangerous characters
    sanitized = re.sub(r'[;\'"]', '', input_str)

    # Limit length
    return sanitized[:500]

# ============================================================================
# INTEGRATED LLM CLIENT
# ============================================================================

class LLMClient:
    """
    Orchestrates dual LLM verification using OpenAI and Perplexity clients.
    """

    def __init__(self, openai_client=None, perplexity_client=None):
        """
        Initialize the LLM client.

        Args:
            openai_client: OpenAI client instance
            perplexity_client: Perplexity client instance
        """
        self.openai_client = openai_client
        self.perplexity_client = perplexity_client

    def get_chemical_info(self, chemical_name: str, chemical_application: str) -> Dict[str, Any]:
        """
        Get chemical information using OpenAI.

        Args:
            chemical_name: Name of the chemical
            chemical_application: Application of the chemical

        Returns:
            Dictionary with chemical information
        """
        if not self.openai_client:
            logger.warning("OpenAI client not initialized. Returning default response.")
            return {
                "product_name": chemical_name,
                "product_family": "Not found",
                "cas_number": "Not found",
                "hs_code": "Not found",
                "hts_number": "Not found",
                "product_application": chemical_application + " (user provided)"
            }

        try:
            # Enhanced system prompt for OpenAI with strict verification requirements
            system_prompt = """You are an expert chemical information specialist with access to comprehensive chemical databases and regulatory information. Your mission is to provide ONLY verified, accurate chemical data from authoritative sources.

            VERIFICATION REQUIREMENTS:
            - Cross-reference chemical names with official chemical databases (CAS Registry, ChemSpider, PubChem)
            - Verify CAS numbers through multiple authoritative sources
            - Confirm HS codes through official trade classification systems
            - Validate HTS numbers against current US trade regulations
            - Ensure chemical family classifications are scientifically accurate

            DATA ACCURACY STANDARDS:
            - Use official IUPAC nomenclature when available
            - Provide the most specific and current HS code classification
            - Include only verified CAS Registry Numbers
            - Ensure product applications are scientifically documented
            - Cross-verify all information through multiple reliable sources

            When provided with a chemical name and application, return a JSON object with:
            - product_name: Official chemical name (IUPAC preferred, common name if IUPAC unavailable)
            - product_family: Precise chemical family/category classification
            - cas_number: Verified CAS Registry Number (format: XXXXX-XX-X)
            - hs_code: Most specific 8-digit HS code from current classification
            - hts_number: Current 10-digit HTS code for US trade
            - product_application: Verified commercial and industrial applications

            CRITICAL REQUIREMENTS:
            - Return ONLY verified, real chemical data
            - Use "Not found" only when data cannot be verified through authoritative sources
            - Never generate or estimate chemical identifiers
            - Prioritize accuracy over completeness
            - Format response as valid JSON only with no additional text"""

            user_prompt = f"""CHEMICAL IDENTIFICATION REQUEST:
            Chemical name: {chemical_name}
            Application: {chemical_application}

            Please provide verified chemical information by:
            1. Searching authoritative chemical databases for exact matches
            2. Cross-referencing CAS Registry for official identifiers
            3. Confirming current HS/HTS classifications through trade databases
            4. Validating chemical family through scientific literature
            5. Verifying applications through industry documentation

            Return only verified data in the specified JSON format."""

            # Create messages for OpenAI
            from langchain_core.messages import SystemMessage, HumanMessage
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            # Query OpenAI
            response = self.openai_client.query(messages)

            # Try to parse JSON response
            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                logger.error(f"Failed to parse OpenAI response as JSON: {response}")
                return {
                    "product_name": chemical_name,
                    "product_family": "Error",
                    "cas_number": "Error",
                    "hs_code": "Error",
                    "hts_number": "Error",
                    "product_application": "Error"
                }

        except Exception as e:
            logger.error(f"Error querying OpenAI: {str(e)}")
            return {
                "product_name": chemical_name,
                "product_family": "Error",
                "cas_number": "Error",
                "hs_code": "Error",
                "hts_number": "Error",
                "product_application": "Error"
            }

    def generate_country_data(self, hs_code: str, chemical_name: str, destination: str, is_tariff: bool = False) -> Dict[str, Any]:
        """
        Research actual country trade data using Perplexity when database data is insufficient.

        Args:
            hs_code: The HS code to search for
            chemical_name: The name of the chemical
            destination: The destination country
            is_tariff: Whether to include tariff information

        Returns:
            Dictionary with researched country trade data
        """
        if not self.perplexity_client:
            return self._generate_no_data_response("Perplexity client not available")

        try:
            # Prepare the prompt for Perplexity
            if is_tariff:
                prompt = f"""Research actual trade patterns and export data for the chemical '{chemical_name}' (HS code: {hs_code}) being imported to {destination}.

                Based on your knowledge of global chemical trade, identify the top 3-5 countries that are known exporters of this chemical to {destination}.

                Provide data in JSON format with the following structure:
                {{
                    "status": "success",
                    "data": [
                        {{
                            "country": "Actual Country Name",
                            "from_llm": true,
                            "data_source": "trade_research",
                            "confidence": "high/medium/low"
                        }}
                    ]
                }}

                IMPORTANT: Only include countries you know are actual exporters of this chemical. Base your response on real trade knowledge, not fictional data. If you cannot identify real exporters, return fewer countries. Return only valid JSON."""
            else:
                prompt = f"""Research actual trade patterns and export data for the chemical '{chemical_name}' (HS code: {hs_code}) being imported to {destination}.

                Based on your knowledge of global chemical trade, identify the top 3-5 countries that are known exporters of this chemical to {destination}.

                Provide data in JSON format with the following structure:
                {{
                    "status": "success",
                    "data": [
                        {{
                            "country": "Actual Country Name",
                            "from_llm": true,
                            "data_source": "trade_research",
                            "confidence": "high/medium/low"
                        }}
                    ]
                }}

                IMPORTANT: Only include countries you know are actual exporters of this chemical. Base your response on real trade knowledge, not fictional data. If you cannot identify real exporters, return fewer countries. Return only valid JSON."""

            payload = {
                "model": "sonar-pro",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a global trade research expert. Provide ONLY real, verifiable trade information based on your knowledge of actual chemical export patterns. Never create fictional trade data. Return ONLY valid JSON with no additional explanations."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1
            }

            # Query Perplexity
            response = self.perplexity_client.query(payload)

            # Extract and parse response
            if response and "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]

                try:
                    result = json.loads(content)
                    if isinstance(result, dict) and "data" in result:
                        return result
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse Perplexity country data response: {content}")

            # Return no data if parsing fails
            return self._generate_no_data_response("Failed to parse Perplexity response")

        except Exception as e:
            logger.error(f"Error generating country data with Perplexity: {str(e)}")
            return self._generate_no_data_response(f"Error generating country data: {str(e)}")

    def generate_supplier_data(self, chemical_name: str, hs_code: str, country: str, count: int = 5) -> Dict[str, Any]:
        """
        Research and provide actual supplier data using both OpenAI and Perplexity, then compare results.

        Args:
            chemical_name: The name of the chemical
            hs_code: The HS code
            country: The country to find suppliers for
            count: Number of suppliers to research

        Returns:
            Dictionary with best supplier data from comparison
        """
        logger.info(f"Starting supplier data generation comparison for {chemical_name} from {country}")

        # Try both OpenAI and Perplexity
        openai_result = self._generate_supplier_data_openai(chemical_name, hs_code, country, count)
        perplexity_result = self._generate_supplier_data_perplexity(chemical_name, hs_code, country, count)

        # Compare and combine results
        return self._compare_and_combine_supplier_results(openai_result, perplexity_result, chemical_name, country)

    def _generate_supplier_data_perplexity(self, chemical_name: str, hs_code: str, country: str, count: int = 5) -> Dict[str, Any]:
        """
        Research supplier data using Perplexity.
        """
        if not self.perplexity_client:
            logger.warning("Perplexity client not available for supplier data generation")
            return self._generate_no_data_response("Perplexity client not available")

        try:
            prompt = f"""CRITICAL TASK: Research and identify ONLY real, verifiable chemical supplier companies for '{chemical_name}' (HS code: {hs_code}) from {country}.

            SEARCH METHODOLOGY - Use multiple verification sources:
            1. Search for companies with official websites and verified business registrations
            2. Cross-reference with industry directories (chemical industry associations, trade databases)
            3. Look for companies mentioned in legitimate trade publications and news articles
            4. Verify through business directories (LinkedIn company pages, Bloomberg, etc.)
            5. Check for regulatory filings and certifications in chemical industry

            SPECIFIC SEARCH TARGETS for {chemical_name} in {country}:
            - Established chemical manufacturers with proven track records
            - Companies with ISO certifications and industry compliance
            - Suppliers with documented export history and international presence
            - Firms with official chemical manufacturing licenses
            - Companies mentioned in legitimate trade reports and industry analyses

            ENHANCED DATA COLLECTION - For each verified company, gather:
            - Official company registration details and founding information
            - Manufacturing capabilities and facility locations
            - Product certifications and quality standards
            - Export markets and international partnerships
            - Financial stability indicators and business scale
            - Recent trade activities and market reputation

            STRICT VERIFICATION REQUIREMENTS:
            - ONLY include companies you can verify through multiple independent sources
            - Each company must have verifiable online presence (official website, business listings)
            - Must have documented involvement in {chemical_name} production/trading
            - Must have evidence of actual business operations (not just registered entities)
            - Cross-verify company names and details across multiple sources

            Return up to {count} VERIFIED companies in this exact JSON format:
            {{
                "status": "success",
                "data": [
                    {{
                        "name": "Exact Official Company Name as registered",
                        "country": "{country}",
                        "company_type": "Manufacturer/Trader/Distributor (verified)",
                        "business_focus": "Specific chemical products and applications",
                        "market_presence": "Documented market reach",
                        "export_capability": "Verified export experience",
                        "verification_sources": "Number of sources verified",
                        "from_llm": true,
                        "data_source": "multi_source_verified"
                    }}
                ]
            }}

            ABSOLUTE REQUIREMENTS:
            - NO fictional or generated company names
            - NO synthetic business information
            - If you cannot verify {count} real companies, return fewer results
            - Use "Data not available" only for specific fields where information cannot be verified
            - Each company must be cross-verified through at least 2 independent sources
            - Return ONLY valid JSON with no additional text or explanations"""

            payload = {
                "model": "sonar-pro",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert chemical industry intelligence analyst with access to real-time web data. Your mission is to identify and verify ONLY legitimate, established chemical companies through rigorous multi-source verification. You have access to current web information including company websites, business directories, trade publications, and regulatory databases. CRITICAL: You must never generate, invent, or create fictional company names or business information. Every piece of data must be verifiable through actual web sources. If you cannot verify sufficient real companies, return fewer results rather than creating any synthetic information. Focus on thorough verification over quantity. Return ONLY valid JSON with no explanations."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1
            }

            # Query Perplexity
            logger.info(f"Querying Perplexity for {count} suppliers of {chemical_name} from {country}")
            response = self.perplexity_client.query(payload)
            logger.info(f"Perplexity response received: {response is not None}")

            # Extract and parse response
            if response and "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]
                logger.info(f"Perplexity response content length: {len(content) if content else 0}")
                logger.info(f"Perplexity response preview: {content[:200] if content else 'No content'}...")

                try:
                    result = json.loads(content)
                    logger.info(f"Perplexity: Successfully parsed JSON. Status: {result.get('status')}, Data count: {len(result.get('data', []))}")
                    if isinstance(result, dict) and "data" in result:
                        # Add source information
                        for item in result.get("data", []):
                            item["data_source"] = "perplexity_verified"
                        return result
                    else:
                        logger.warning(f"Perplexity: Invalid result structure: {result}")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse Perplexity supplier data response: {content}")
                    logger.error(f"JSON decode error: {str(e)}")
            else:
                logger.warning(f"Invalid Perplexity response structure: {response}")

            # Return no data if parsing fails
            return self._generate_no_data_response("Failed to parse Perplexity supplier response")

        except Exception as e:
            logger.error(f"Error generating supplier data with Perplexity: {str(e)}")
            return self._generate_no_data_response(f"Error generating supplier data: {str(e)}")

    def _generate_supplier_data_openai(self, chemical_name: str, hs_code: str, country: str, count: int = 5) -> Dict[str, Any]:
        """
        Research supplier data using OpenAI.
        """
        if not self.openai_client:
            logger.warning("OpenAI client not available for supplier data generation")
            return self._generate_no_data_response("OpenAI client not available")

        try:
            prompt = f"""CRITICAL TASK: Research and identify ONLY real, verifiable chemical supplier companies for '{chemical_name}' (HS code: {hs_code}) from {country}.

            SEARCH METHODOLOGY - Use multiple verification sources:
            1. Search for companies with official websites and verified business registrations
            2. Cross-reference with industry directories (chemical industry associations, trade databases)
            3. Look for companies mentioned in legitimate trade publications and news articles
            4. Verify through business directories (LinkedIn company pages, Bloomberg, etc.)
            5. Check for regulatory filings and certifications in chemical industry

            SPECIFIC SEARCH TARGETS for {chemical_name} in {country}:
            - Established chemical manufacturers with proven track records
            - Companies with ISO certifications and industry compliance
            - Suppliers with documented export history and international presence
            - Firms with official chemical manufacturing licenses
            - Companies mentioned in legitimate trade reports and industry analyses

            ENHANCED DATA COLLECTION - For each verified company, gather:
            - Official company registration details and founding information
            - Manufacturing capabilities and facility locations
            - Product certifications and quality standards
            - Export markets and international partnerships
            - Financial stability indicators and business scale
            - Recent trade activities and market reputation
            - SPECIFIC EXPORT DATA: transaction volumes, pricing, shipment frequencies

            STRICT VERIFICATION REQUIREMENTS:
            - ONLY include companies you can verify through multiple independent sources
            - Each company must have verifiable online presence (official website, business listings)
            - Must have documented involvement in {chemical_name} production/trading
            - Must have evidence of actual business operations (not just registered entities)
            - Cross-verify company names and details across multiple sources

            Return up to {count} VERIFIED companies in this exact JSON format:
            {{
                "status": "success",
                "data": [
                    {{
                        "name": "Exact Official Company Name as registered",
                        "country": "{country}",
                        "company_type": "Manufacturer/Trader/Distributor (verified)",
                        "business_focus": "Specific chemical products and applications",
                        "market_presence": "Documented market reach",
                        "export_capability": "Verified export experience",
                        "export_data": {{
                            "annual_volume": "Verified annual export volume if available",
                            "key_markets": "Primary export destinations",
                            "price_range": "Typical price range per ton if available",
                            "shipment_frequency": "Typical shipment patterns"
                        }},
                        "verification_sources": "Number of sources verified",
                        "from_llm": true,
                        "data_source": "openai_verified"
                    }}
                ]
            }}

            ABSOLUTE REQUIREMENTS:
            - NO fictional or generated company names
            - NO synthetic business information
            - If you cannot verify {count} real companies, return fewer results
            - Use "Data not available" only for specific fields where information cannot be verified
            - Each company must be cross-verified through at least 2 independent sources
            - Return ONLY valid JSON with no additional text or explanations"""

            # Query OpenAI
            logger.info(f"Querying OpenAI for {count} suppliers of {chemical_name} from {country}")
            response = self.openai_client.query(prompt)
            logger.info(f"OpenAI response received: {response is not None}")

            if response:
                logger.info(f"OpenAI response content length: {len(response) if response else 0}")
                logger.info(f"OpenAI response preview: {response[:200] if response else 'No content'}...")

                try:
                    # Clean up response if it contains markdown code blocks
                    content = response.strip()
                    if content.startswith("```json"):
                        content = content[7:]
                    if content.endswith("```"):
                        content = content[:-3]
                    content = content.strip()

                    result = json.loads(content)
                    logger.info(f"OpenAI: Successfully parsed JSON. Status: {result.get('status')}, Data count: {len(result.get('data', []))}")
                    if isinstance(result, dict) and "data" in result:
                        # Add source information
                        for item in result.get("data", []):
                            item["data_source"] = "openai_verified"
                        return result
                    else:
                        logger.warning(f"OpenAI: Invalid result structure: {result}")
                except json.JSONDecodeError as e:
                    logger.error(f"OpenAI: Failed to parse supplier data response: {content}")
                    logger.error(f"OpenAI: JSON decode error: {str(e)}")
            else:
                logger.warning(f"OpenAI: No response received")

            # Return no data if parsing fails
            return self._generate_no_data_response("OpenAI: Failed to parse supplier response")

        except Exception as e:
            logger.error(f"Error generating supplier data with OpenAI: {str(e)}")
            return self._generate_no_data_response(f"OpenAI error: {str(e)}")

    def _compare_and_combine_supplier_results(self, openai_result: Dict[str, Any], perplexity_result: Dict[str, Any], chemical_name: str, country: str) -> Dict[str, Any]:
        """
        Compare OpenAI and Perplexity results and combine the best data.
        """
        logger.info("=== LLM COMPARISON ANALYSIS ===")

        # Extract data from both sources
        openai_data = openai_result.get("data", []) if openai_result.get("status") == "success" else []
        perplexity_data = perplexity_result.get("data", []) if perplexity_result.get("status") == "success" else []

        logger.info(f"OpenAI returned {len(openai_data)} suppliers")
        logger.info(f"Perplexity returned {len(perplexity_data)} suppliers")

        # Analyze data quality
        openai_score = self._calculate_data_quality_score(openai_data, "OpenAI")
        perplexity_score = self._calculate_data_quality_score(perplexity_data, "Perplexity")

        logger.info(f"OpenAI quality score: {openai_score}")
        logger.info(f"Perplexity quality score: {perplexity_score}")

        # Combine unique suppliers from both sources
        combined_suppliers = []
        seen_names = set()

        # Add all suppliers with source tracking
        all_suppliers = []

        # Process OpenAI suppliers
        for supplier in openai_data:
            supplier_copy = supplier.copy()
            supplier_copy["llm_source"] = "openai"
            supplier_copy["data_source"] = "openai_verified"
            all_suppliers.append(supplier_copy)

        # Process Perplexity suppliers
        for supplier in perplexity_data:
            supplier_copy = supplier.copy()
            supplier_copy["llm_source"] = "perplexity"
            supplier_copy["data_source"] = "perplexity_verified"
            all_suppliers.append(supplier_copy)

        # Remove duplicates and keep the best version
        for supplier in all_suppliers:
            name = supplier.get("name", "").lower().strip()
            if name and name not in seen_names:
                seen_names.add(name)
                combined_suppliers.append(supplier)
            elif name in seen_names:
                # Find existing supplier and compare quality
                for i, existing in enumerate(combined_suppliers):
                    if existing.get("name", "").lower().strip() == name:
                        # Keep the one with more complete data
                        if self._supplier_has_better_data(supplier, existing):
                            combined_suppliers[i] = supplier
                            logger.info(f"Replaced {name} with better data from {supplier.get('llm_source')}")
                        break

        # Sort by data completeness
        combined_suppliers.sort(key=lambda x: self._calculate_supplier_completeness(x), reverse=True)

        # Create summary
        openai_count = len([s for s in combined_suppliers if s.get("llm_source") == "openai"])
        perplexity_count = len([s for s in combined_suppliers if s.get("llm_source") == "perplexity"])

        logger.info(f"Final combined result: {len(combined_suppliers)} suppliers ({openai_count} from OpenAI, {perplexity_count} from Perplexity)")

        if combined_suppliers:
            return {
                "status": "success",
                "data": combined_suppliers,
                "comparison_summary": {
                    "openai_suppliers": len(openai_data),
                    "perplexity_suppliers": len(perplexity_data),
                    "openai_quality_score": openai_score,
                    "perplexity_quality_score": perplexity_score,
                    "final_openai_count": openai_count,
                    "final_perplexity_count": perplexity_count,
                    "total_unique_suppliers": len(combined_suppliers)
                }
            }
        else:
            return self._generate_no_data_response("Both OpenAI and Perplexity failed to find valid suppliers")

    def _calculate_data_quality_score(self, suppliers: list, source_name: str) -> float:
        """Calculate quality score for supplier data."""
        if not suppliers:
            return 0.0

        total_score = 0
        for supplier in suppliers:
            score = 0

            # Basic info (20 points)
            if supplier.get("name"):
                score += 10
            if supplier.get("company_type"):
                score += 5
            if supplier.get("business_focus"):
                score += 5

            # Export data (30 points)
            export_data = supplier.get("export_data", {})
            if export_data:
                if export_data.get("annual_volume"):
                    score += 10
                if export_data.get("key_markets"):
                    score += 10
                if export_data.get("price_range"):
                    score += 10

            # Business details (30 points)
            if supplier.get("market_presence"):
                score += 10
            if supplier.get("export_capability"):
                score += 10
            if supplier.get("verification_sources"):
                score += 10

            # Contact/additional info (20 points)
            if supplier.get("contact"):
                score += 10
            if supplier.get("additional_info"):
                score += 10

            total_score += score

        avg_score = total_score / len(suppliers)
        logger.info(f"{source_name} average completeness: {avg_score:.1f}/100")
        return avg_score

    def _supplier_has_better_data(self, supplier1: dict, supplier2: dict) -> bool:
        """Compare two suppliers and return True if supplier1 has better data."""
        score1 = self._calculate_supplier_completeness(supplier1)
        score2 = self._calculate_supplier_completeness(supplier2)
        return score1 > score2

    def _calculate_supplier_completeness(self, supplier: dict) -> int:
        """Calculate completeness score for a single supplier."""
        score = 0

        # Basic fields
        if supplier.get("name"): score += 1
        if supplier.get("company_type"): score += 1
        if supplier.get("business_focus"): score += 1
        if supplier.get("market_presence"): score += 1
        if supplier.get("export_capability"): score += 1

        # Export data
        export_data = supplier.get("export_data", {})
        if export_data:
            if export_data.get("annual_volume"): score += 2
            if export_data.get("key_markets"): score += 2
            if export_data.get("price_range"): score += 2
            if export_data.get("shipment_frequency"): score += 1

        return score

    def generate_detailed_supplier_profile(self, supplier_name: str, chemical_name: str, hs_code: str, country: str) -> Dict[str, Any]:
        """
        Generate comprehensive supplier profile using Perplexity web search with enhanced contact information scraping.

        Args:
            supplier_name: Name of the supplier
            chemical_name: Name of the chemical
            hs_code: HS code of the product
            country: Country of the supplier

        Returns:
            Dictionary with detailed supplier profile
        """
        logger.info(f"Starting detailed supplier profile generation for: {supplier_name} from {country}")
        logger.info(f"Perplexity client available: {self.perplexity_client is not None}")

        if not self.perplexity_client:
            logger.warning(f"Perplexity client not available, using fallback for {supplier_name}")
            return self._generate_fallback_supplier_profile(supplier_name, chemical_name, country)

        try:
            # Enhanced prompt for comprehensive web research and verification
            prompt = f"""COMPREHENSIVE INTELLIGENCE GATHERING: Research and verify detailed information about {supplier_name}, a chemical supplier from {country} specializing in {chemical_name} (HS code: {hs_code}).

            MULTI-SOURCE VERIFICATION STRATEGY:
            1. PRIMARY SOURCES - Official company website, regulatory filings, business registrations
            2. BUSINESS DIRECTORIES - LinkedIn company pages, Bloomberg, D&B, industry databases
            3. TRADE PUBLICATIONS - Chemical industry magazines, trade journals, market reports
            4. NEWS & PRESS - Recent news articles, press releases, industry announcements
            5. REGULATORY SOURCES - Chemical industry certifications, export licenses, compliance records
            6. FINANCIAL SOURCES - Company financial reports, credit ratings, business performance data

            ENHANCED DATA EXTRACTION REQUIREMENTS:
            1. COMPANY VERIFICATION:
               - Confirm company exists through multiple independent sources
               - Verify official business registration and legal status
               - Cross-check company name variations and subsidiaries
               - Validate operational status and business activity

            2. BUSINESS INTELLIGENCE:
               - Founding year, ownership structure, key executives
               - Annual revenue, employee count, market capitalization
               - Manufacturing facilities locations and capacity
               - Technology platforms and production capabilities

            3. PRODUCT & MARKET ANALYSIS:
               - Specific {chemical_name} products and grades manufactured
               - Quality certifications and industry standards compliance
               - Target markets and customer segments
               - Export destinations and international partnerships

            4. COMPETITIVE POSITIONING:
               - Market share and industry ranking
               - Unique value propositions and competitive advantages
               - Recent innovations and technological developments
               - Strategic partnerships and joint ventures

            5. VERIFIED CONTACT INTELLIGENCE:
               - Official website URL (verified and accessible)
               - Primary business email (from official sources)
               - Direct phone numbers with country codes
               - Complete headquarters address with postal codes
               - Regional office locations and contact details

            STRICT VERIFICATION PROTOCOLS:
            - Every data point must be verifiable through actual web sources
            - Cross-reference information across minimum 2 independent sources
            - Prioritize official company sources over third-party information
            - Flag any information that cannot be independently verified
            - Use empty strings for unverifiable data (never use placeholder text)

            Return comprehensive intelligence in this exact JSON format:
            {{
                "company_name": "{supplier_name}",
                "overview": "Detailed company background with verified founding year, ownership, and business model",
                "products": "Specific {chemical_name} products, grades, applications, and technical specifications",
                "manufacturing_capabilities": "Verified production facilities, capacity, technology, and quality systems",
                "export_markets": "Documented export destinations, international presence, and distribution channels",
                "certifications": ["Verified ISO standards", "Industry certifications", "Regulatory approvals"],
                "competitive_advantages": ["Verified market strengths", "Technological capabilities", "Strategic advantages"],
                "contact": {{
                    "website": "https://verified-official-website.com",
                    "email": "<EMAIL>",
                    "phone": "+country-code-verified-number",
                    "address": "Complete verified headquarters address with postal code"
                }},
                "additional_info": "Recent developments, financial performance, strategic initiatives",
                "verification_status": "Multi-source verified/Partially verified/Limited verification",
                "data_sources": ["Source 1", "Source 2", "Source 3"]
            }}

            ABSOLUTE REQUIREMENTS:
            - NO synthetic or generated information
            - NO placeholder or template data
            - Use "" for any field where real data cannot be verified
            - Prioritize data quality and verification over completeness
            - Return ONLY valid JSON with no additional text"""

            payload = {
                "model": "sonar-pro",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an elite chemical industry intelligence analyst with real-time web access and advanced verification capabilities. Your expertise lies in conducting thorough multi-source research to gather and verify comprehensive business intelligence on chemical companies. You have access to current web data including company websites, business registries, trade databases, financial reports, and industry publications. Your mission is to extract and verify ONLY authentic, real business information through rigorous cross-referencing. You must never generate, assume, or create any synthetic data. Every piece of information must be traceable to verifiable web sources. If data cannot be verified, use empty strings rather than placeholders. Focus on thorough verification and data quality over speed. Return ONLY valid JSON with no explanations or commentary."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1  # Lower temperature for more focused, accurate results
            }

            # Query Perplexity with enhanced logging
            logger.info(f"Querying Perplexity for {supplier_name} with model: {payload['model']}")
            response = self.perplexity_client.query(payload)
            logger.info(f"Perplexity response received: {response is not None}")

            if response:
                logger.debug(f"Perplexity response keys: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")

            # Extract and parse response
            if response and "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]
                logger.info(f"Perplexity content length: {len(content) if content else 0}")
                logger.debug(f"Raw Perplexity content preview: {content[:200] if content else 'No content'}...")

                try:
                    # Clean up response if it contains markdown code blocks
                    content = content.strip()
                    if content.startswith("```json"):
                        content = content[7:]
                    if content.endswith("```"):
                        content = content[:-3]
                    content = content.strip()

                    # Try to parse as JSON
                    result = json.loads(content)
                    logger.info(f"Successfully parsed JSON from Perplexity for {supplier_name}")
                    logger.debug(f"Parsed result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

                    if isinstance(result, dict):
                        # Post-process the result to ensure proper blank value handling
                        cleaned_result = self._clean_supplier_profile_data(result, supplier_name)
                        logger.info(f"Successfully generated detailed profile for {supplier_name}")
                        return {
                            "status": "success",
                            "data": cleaned_result,
                            "message": "Comprehensive supplier profile generated from web research and intelligence gathering",
                            "sources": ["Perplexity AI"]
                        }
                    else:
                        logger.warning(f"Perplexity returned non-dict result for {supplier_name}: {type(result)}")

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse Perplexity detailed supplier response for {supplier_name}")
                    logger.error(f"JSON decode error: {str(e)}")
                    logger.error(f"Raw content that failed to parse: {content[:200]}...")

                    # FALLBACK: If Perplexity returns non-JSON, use web scraping directly
                    logger.info(f"Perplexity returned non-JSON response, falling back to web scraping for {supplier_name}")
                    try:
                        scraped_contact = web_scraper.scrape_company_website(supplier_name)
                        logger.info(f"Web scraping fallback results: {scraped_contact}")

                        if any(scraped_contact.values()):
                            # Create a basic profile with scraped contact info
                            fallback_profile = {
                                "company_name": supplier_name,
                                "overview": f"{supplier_name} is a chemical supplier based in {country}.",
                                "products": f"Chemical products including {chemical_name}",
                                "manufacturing_capabilities": "Manufacturing capabilities not specified",
                                "export_markets": "Export markets not specified",
                                "certifications": [],
                                "competitive_advantages": [],
                                "contact": {
                                    "website": scraped_contact.get("website", ""),
                                    "email": scraped_contact.get("email", ""),
                                    "phone": scraped_contact.get("phone", ""),
                                    "address": scraped_contact.get("address", "")
                                },
                                "additional_info": "Profile generated from web scraping due to LLM parsing issues",
                                "verification_status": "Web scraping verified",
                                "data_sources": ["Web Scraping"]
                            }

                            cleaned_result = self._clean_supplier_profile_data(fallback_profile, supplier_name)
                            logger.info(f"Successfully generated web scraping fallback profile for {supplier_name}")
                            return {
                                "status": "success",
                                "data": cleaned_result,
                                "message": "Supplier profile generated from web scraping (LLM response was not in JSON format)",
                                "sources": ["Web Scraping"]
                            }
                    except Exception as scraping_error:
                        logger.error(f"Web scraping fallback also failed for {supplier_name}: {str(scraping_error)}")
            else:
                logger.warning(f"No valid response from Perplexity for {supplier_name}")
                if response:
                    logger.debug(f"Response structure: {response}")

            # Return fallback profile if parsing fails
            logger.info(f"Falling back to basic profile for {supplier_name}")
            return self._generate_fallback_supplier_profile(supplier_name, chemical_name, country)

        except Exception as e:
            logger.error(f"Error generating detailed supplier profile with Perplexity: {str(e)}")
            return self._generate_fallback_supplier_profile(supplier_name, chemical_name, country)

    def _clean_supplier_profile_data(self, data: Dict[str, Any], supplier_name: str) -> Dict[str, Any]:
        """
        Clean and standardize supplier profile data to ensure proper blank value handling.

        Args:
            data: Raw supplier profile data from LLM
            supplier_name: Original supplier name

        Returns:
            Cleaned supplier profile data
        """
        # Define the expected structure with default empty values
        cleaned_data = {
            "company_name": supplier_name,  # Always use the original supplier name
            "overview": "",
            "products": "",
            "manufacturing_capabilities": "",
            "export_markets": "",
            "certifications": [],
            "competitive_advantages": [],
            "contact": {
                "website": "",
                "email": "",
                "phone": "",
                "address": ""
            },
            "additional_info": ""
        }

        # List of phrases that should be converted to empty strings
        unavailable_phrases = [
            "information not available",
            "not available",
            "n/a",
            "na",
            "unknown",
            "not found",
            "no information",
            "data not available",
            "information unavailable",
            "not specified",
            "not provided",
            "unavailable"
        ]

        def clean_value(value):
            """Clean individual values by converting unavailable indicators to empty strings."""
            if value is None:
                return ""

            if isinstance(value, str):
                cleaned = value.strip()
                # Check if the value indicates unavailable information
                if cleaned.lower() in unavailable_phrases:
                    return ""
                return cleaned

            if isinstance(value, list):
                # Clean list items and remove unavailable entries
                cleaned_list = []
                for item in value:
                    cleaned_item = clean_value(item)
                    if cleaned_item:  # Only add non-empty items
                        cleaned_list.append(cleaned_item)
                return cleaned_list

            return value

        # Update cleaned_data with values from the input data
        for key in cleaned_data.keys():
            if key in data:
                if key == "contact" and isinstance(data[key], dict):
                    # Special handling for contact information
                    for contact_key in cleaned_data["contact"].keys():
                        if contact_key in data[key]:
                            cleaned_data["contact"][contact_key] = clean_value(data[key][contact_key])
                else:
                    cleaned_data[key] = clean_value(data[key])

        # ENHANCED: Use web scraping to fill in missing contact information
        contact_info = cleaned_data["contact"]
        missing_fields = [k for k, v in contact_info.items() if not v]

        if missing_fields:  # If any contact info is missing
            try:
                logger.info(f"Attempting web scraping for missing contact info for {supplier_name}")
                logger.info(f"Missing fields: {missing_fields}")

                scraped_contact = web_scraper.scrape_company_website(supplier_name)
                logger.info(f"Web scraping results for {supplier_name}: {scraped_contact}")

                # Fill in missing contact information with scraped data
                filled_fields = []
                for key, value in scraped_contact.items():
                    if value and not contact_info.get(key):  # Only fill if current value is empty
                        contact_info[key] = value
                        filled_fields.append(key)
                        logger.info(f"Web scraping found {key}: {value[:50]}..." if len(value) > 50 else f"Web scraping found {key}: {value}")

                if filled_fields:
                    logger.info(f"Successfully filled contact fields via web scraping: {filled_fields}")
                else:
                    logger.warning(f"Web scraping did not find any missing contact information for {supplier_name}")

            except Exception as e:
                logger.warning(f"Web scraping failed for {supplier_name}: {str(e)}")

        return cleaned_data

    def _generate_fallback_supplier_profile(self, supplier_name: str, chemical_name: str, country: str) -> Dict[str, Any]:
        """
        Generate a fallback supplier profile when Perplexity is not available.

        Args:
            supplier_name: Name of the supplier
            chemical_name: Name of the chemical
            country: Country of the supplier

        Returns:
            Dictionary with basic supplier profile
        """
        return {
            "status": "success",
            "data": {
                "company_name": supplier_name,
                "overview": f"{supplier_name} is a chemical supplier based in {country} specializing in {chemical_name} and related chemical products.",
                "products": f"Supplier of {chemical_name} and related chemical products for various industrial applications.",
                "manufacturing_capabilities": "",  # Empty string when no data available
                "export_markets": f"Exports to various international markets from {country}.",
                "certifications": [],  # Empty array when no data available
                "competitive_advantages": ["Established supplier", "Quality products", "Reliable delivery"],
                "contact": {
                    "website": "",  # Empty string when no data available
                    "email": "",   # Empty string when no data available
                    "phone": "",   # Empty string when no data available
                    "address": f"{country}" if country != "Unknown" else ""
                },
                "additional_info": "Basic profile generated without web research capabilities."
            },
            "message": "Basic supplier profile generated (web research not available)"
        }

    def _generate_no_data_response(self, message: str) -> Dict[str, Any]:
        """
        Generate a standardized "no data" response when valid data cannot be obtained.

        Args:
            message: The reason why no data is available

        Returns:
            Dictionary with no data status
        """
        return {
            "status": "warning",
            "message": message,
            "data": []
        }



# Initialize enhanced services for new endpoints
db_client = DatabaseClient(DB_CONFIG)
openai_client = OpenAIClient()
perplexity_client = PerplexityClient()
llm_client = LLMClient(openai_client, perplexity_client)
ranking_service = TradeRankerService()

# Initialize web scraper for contact information extraction
web_scraper = WebScraper()

# Integrated Country Service functionality
class IntegratedCountryService:
    """
    Integrated country service with fallback mechanisms.
    """

    def __init__(self, db_client: DatabaseClient, llm_client: Optional[LLMClient] = None):
        """
        Initialize the integrated country service.

        Args:
            db_client: Database client for trade data queries
            llm_client: LLM client for fallback data generation
        """
        self.db_client = db_client
        self.llm_client = llm_client
        self.tariff_api_url = "http://13.201.135.9:8082/tariff"

        # Import configuration for HTS2Duty integration
        from app.config import USE_LOCAL_HTS2DUTY
        self.use_local_hts2duty = USE_LOCAL_HTS2DUTY

    def get_top_countries(self, hs_code: str, chemical_name: str, destination: str, months: int = 12) -> CountryLookupResult:
        """
        Get top countries exporting a product with given HS code.

        Args:
            hs_code: The HS code to search for
            chemical_name: The name of the chemical
            destination: The destination country
            months: Number of months of data to retrieve

        Returns:
            CountryLookupResult with the lookup results
        """
        try:
            # Format HS code (remove dots, take first 4 digits)
            formatted_hs_code = hs_code.replace(".", "")[:4]

            # Build and execute query
            query, params = self._build_query_top_countries(formatted_hs_code, destination, months, chemical_name)
            result = self.db_client.execute_query(query, params)

            if result["status"] != "success":
                logger.warning(f"Database query failed: {result.get('message', 'Unknown error')}")
                # Try LLM fallback
                if self.llm_client:
                    return self._generate_country_fallback(hs_code, chemical_name, destination, False)
                else:
                    return CountryLookupResult.error("Database query failed and no LLM fallback available")

            # Transform API data to match expected structure
            transformed_data = []

            for item in result.get("data", []):
                # Handle potential missing or None values
                transaction_count = item.get("shipment_count", 0) or 0
                export_volume = item.get("total_quantity", 0) or 0
                avg_price = item.get("average_fob", 0) or 0

                # Calculate total export value
                total_export_value = avg_price * export_volume if avg_price and export_volume else 0

                transformed_item = {
                    "country": truncate_text(item.get("origin_country", "Unknown"), 30),
                    "transaction_count": transaction_count,
                    "export_volume_tons": export_volume,
                    "avg_price_per_ton": avg_price,
                    "total_export_value": total_export_value,
                    "unit": item.get("global_std_unit_id", "KGS"),
                    "from_llm": False,
                    # Add formatted display fields
                    "formatted_transaction_count": format_number(transaction_count, 0),
                    "formatted_export_volume": format_weight(export_volume, "tons"),
                    "formatted_avg_price": format_currency(avg_price),
                    "formatted_total_value": format_currency(total_export_value)
                }

                transformed_data.append(transformed_item)

            # If no data was found, try to research data using LLM
            if not transformed_data and self.llm_client:
                logger.info(f"No data found in database, researching with LLM for {chemical_name}, {hs_code}")
                return self._generate_country_fallback(hs_code, chemical_name, destination, False)

            return CountryLookupResult.success(
                data=transformed_data,
                message="Data retrieved from database" if transformed_data else "No data found"
            )

        except Exception as e:
            logger.error(f"Error getting top countries: {str(e)}")
            # Try LLM fallback on error
            if self.llm_client:
                return self._generate_country_fallback(hs_code, chemical_name, destination, False)
            else:
                return CountryLookupResult.error(f"Error getting top countries: {str(e)}")

    def get_top_countries_with_tariff(self, hs_code: str, chemical_name: str, destination: str, months: int = 12) -> CountryLookupResult:
        """
        Get top countries exporting a product with tariff information.

        Args:
            hs_code: The HS code to search for
            chemical_name: The name of the chemical
            destination: The destination country
            months: Number of months of data to retrieve

        Returns:
            CountryLookupResult with the lookup results including tariff data
        """
        try:
            # First get the basic country data
            basic_result = self.get_top_countries(hs_code, chemical_name, destination, months)

            if basic_result.status != "success" or not basic_result.data:
                # Try LLM fallback with tariff data
                if self.llm_client:
                    return self._generate_country_fallback(hs_code, chemical_name, destination, True)
                else:
                    return basic_result

            # Enrich with tariff data
            enriched_countries = []

            for country_data in basic_result.data:
                country_name = country_data.get("country", "")

                # Fetch tariff information
                tariff_info = self._fetch_tariff_data(hs_code, country_name)

                # Add tariff information to country data
                if tariff_info and "error" not in tariff_info:
                    duty_percentage = self._extract_duty_percentage(tariff_info.get("duty"))
                    country_data.update({
                        "duty": tariff_info.get("duty"),
                        "footer_duty": tariff_info.get("footer_duty"),
                        "new_tariff": tariff_info.get("new_tariff"),
                        "ad": tariff_info.get("ad"),
                        "cvd": tariff_info.get("cvd"),
                        "total_duty": tariff_info.get("total_duty"),
                        "proposed_tariff": tariff_info.get("proposed_tariff"),
                        "duty_percentage": duty_percentage,
                        # Add formatted display fields for tariff
                        "formatted_duty_percentage": format_percentage(duty_percentage),
                        "formatted_total_duty": tariff_info.get("total_duty", "N/A")
                    })
                else:
                    # Add default tariff information
                    country_data.update({
                        "duty": "N/A",
                        "total_duty": "N/A",
                        "duty_percentage": 0.0,
                        "formatted_duty_percentage": "N/A",
                        "formatted_total_duty": "N/A"
                    })

                enriched_countries.append(country_data)

            return CountryLookupResult.success(
                data=enriched_countries,
                message="Data retrieved with tariff information"
            )

        except Exception as e:
            logger.error(f"Error getting countries with tariff: {str(e)}")
            # Try LLM fallback on error
            if self.llm_client:
                return self._generate_country_fallback(hs_code, chemical_name, destination, True)
            else:
                return CountryLookupResult.error(f"Error getting countries with tariff: {str(e)}")

    def _generate_country_fallback(self, hs_code: str, chemical_name: str, destination: str, is_tariff: bool) -> CountryLookupResult:
        """
        Research country data using LLM as fallback when database data is unavailable.

        Args:
            hs_code: The HS code
            chemical_name: The chemical name
            destination: The destination country
            is_tariff: Whether to include tariff information

        Returns:
            CountryLookupResult with researched data or no data if LLM fails to find valid information
        """
        try:
            llm_result = self.llm_client.generate_country_data(hs_code, chemical_name, destination, is_tariff)

            if llm_result["status"] == "success" and llm_result.get("data"):
                return CountryLookupResult.success(
                    data=llm_result["data"],
                    message="Data researched from LLM knowledge base as fallback mechanism"
                )
            else:
                # LLM failed to research valid data - return no data
                return CountryLookupResult.warning(
                    message=f"No valid data available for HS code {hs_code} and chemical {chemical_name}. LLM could not find reliable trade information.",
                    data=[]
                )
        except Exception as e:
            logger.error(f"Error in LLM fallback: {str(e)}")
            return CountryLookupResult.warning(
                message=f"No valid data available for HS code {hs_code} and chemical {chemical_name}. Data sources unavailable.",
                data=[]
            )

    def _fetch_tariff_data(self, hs_code: str, country: str) -> Dict[str, Any]:
        """
        Fetch tariff data for a given HS code and country.
        Uses local HTS2Duty service if enabled, otherwise falls back to external API.

        Args:
            hs_code: The HS code
            country: The country name

        Returns:
            Dictionary with tariff data
        """
        try:
            # Use local HTS2Duty service if enabled
            if self.use_local_hts2duty:
                logger.debug("Using local HTS2Duty service for tariff data")
                from app.services.hts2duty_service import get_hts2duty_service
                hts2duty_service = get_hts2duty_service()
                tariff_data = hts2duty_service.get_tariff_data(hs_code, country)

                # Check if there was an error in the response
                if 'error' in tariff_data:
                    logger.warning(f"HTS2Duty service error for {country}: {tariff_data['error']}")
                    # Fall back to external API if local service fails
                    return self._fetch_external_tariff_data(hs_code, country)

                logger.debug(f"Received tariff data from HTS2Duty for {country}")
                return tariff_data
            else:
                # Use external API
                return self._fetch_external_tariff_data(hs_code, country)

        except Exception as e:
            logger.error(f"Error in _fetch_tariff_data for {country}: {str(e)}")
            # Try external API as fallback if local service fails
            if self.use_local_hts2duty:
                logger.info(f"Falling back to external API for {country}")
                try:
                    return self._fetch_external_tariff_data(hs_code, country)
                except Exception as fallback_error:
                    logger.error(f"External API fallback also failed for {country}: {str(fallback_error)}")
            return {"error": f"Exception in tariff API call: {str(e)}"}

    def _fetch_external_tariff_data(self, hs_code: str, country: str) -> Dict[str, Any]:
        """Fetch tariff data from external API"""
        try:
            response = requests.get(
                self.tariff_api_url,
                params={"hs_code": hs_code, "country": country},
                timeout=5
            )
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Tariff API returned status {response.status_code} for {country}")
                return {"error": f"Tariff API error {response.status_code}"}
        except Exception as e:
            logger.warning(f"Exception in external tariff API call for {country}: {str(e)}")
            return {"error": f"Exception in external tariff API call: {str(e)}"}

    def _extract_duty_percentage(self, duty_str: Optional[str]) -> float:
        """
        Extract duty percentage from duty string.

        Args:
            duty_str: Duty string (e.g., "5.5%")

        Returns:
            Duty percentage as float
        """
        if not duty_str:
            return 0.0

        try:
            # Remove % and convert to float
            return float(duty_str.replace('%', '').strip())
        except:
            return 0.0

    def _build_query_top_countries(self, hs_code: str, destination: str, months: int, chemical_name: Optional[str] = None) -> tuple:
        """
        Build query to retrieve top countries exporting products.

        Args:
            hs_code: HS code (first 4 digits)
            destination: Destination country code
            months: Number of months to look back
            chemical_name: Optional chemical name for filtering

        Returns:
            Tuple of (query, params)
        """
        # Initialize parameters
        params = [destination, months]

        # Build product description condition if chemical name is provided
        product_desc_condition = ""
        if chemical_name:
            # Simple ILIKE condition for chemical name
            product_desc_condition = "AND product_desc ILIKE %s"
            params.append(f"%{chemical_name}%")

        # Add HS code parameter
        params.append(hs_code)

        query = f"""
        WITH filtered_by_hs AS (
            SELECT
                parent_coo,
                global_std_unit_id,
                std_qty,
                fob_value_usd,
                date,
                product_desc,
                gross_wt,
                hs_code
            FROM
                volza_trade_data
            WHERE
                parent_cod = %s
                AND date >= CURRENT_DATE - INTERVAL '%s month'
                {product_desc_condition}
                AND SUBSTRING(hs_code, 1, 4) = %s
        ),
        aggregated AS (
            SELECT
                parent_coo AS origin_country,
                global_std_unit_id,
                SUM(std_qty) AS total_quantity,
                AVG(fob_value_usd) AS average_fob,
                MIN(fob_value_usd) AS minimum_fob,
                COUNT(*) AS shipment_count,
                SUM(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE 0 END) AS total_gross_weight,
                AVG(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE NULL END) AS average_gross_weight
            FROM
                filtered_by_hs
            WHERE
                CASE
                    WHEN gross_wt ~ '^[0-9]+$' AND std_qty IS NOT NULL THEN
                        GREATEST(gross_wt::INTEGER, std_qty) >= 10
                    WHEN gross_wt ~ '^[0-9]+$' THEN
                        gross_wt::INTEGER >= 10
                    WHEN std_qty IS NOT NULL THEN
                        std_qty >= 10
                    ELSE FALSE
                END
            GROUP BY
                parent_coo, global_std_unit_id
            HAVING
                COUNT(*) >= 1
            ORDER BY
                total_quantity DESC
        ),
        ranked AS (
            SELECT *,
                ROW_NUMBER() OVER (PARTITION BY origin_country ORDER BY total_quantity DESC) AS rn
            FROM aggregated
        )

        SELECT *
        FROM ranked
        WHERE rn = 1
        ORDER BY total_quantity DESC;
        """

        return query, tuple(params)


# Integrated Supplier Service functionality
class IntegratedSupplierService:
    """
    Integrated supplier service with ranking and data enrichment.
    """

    def __init__(self, db_client: DatabaseClient, llm_client: Optional[LLMClient] = None):
        """
        Initialize the supplier service.

        Args:
            db_client: Database client for trade data queries
            llm_client: LLM client for fallback data generation
        """
        self.db_client = db_client
        self.llm_client = llm_client

    def get_top_suppliers(self, hs_code: str, country: str, destination: str, chemical_name: str, months: int = 12, limit: int = 10) -> SupplierLookupResult:
        """
        Get top suppliers from a specific country for a given HS code.

        Args:
            hs_code: The HS code to search for
            country: The origin country
            destination: The destination country
            chemical_name: The name of the chemical
            months: Number of months of data to retrieve
            limit: Maximum number of suppliers to return

        Returns:
            SupplierLookupResult with the lookup results
        """
        try:
            # Format HS code (remove dots, take first 4 digits)
            formatted_hs_code = hs_code.replace(".", "")[:4]

            # Debug logging
            logger.info(f"Searching for suppliers: HS={formatted_hs_code}, Country={country}, Destination={destination}, Chemical={chemical_name}, Months={months}, Limit={limit}")

            # Build and execute query
            query, params = self._build_query_top_suppliers(formatted_hs_code, country, destination, months, chemical_name, limit)
            logger.info(f"Executing query with params: {params}")

            result = self.db_client.execute_query(query, params)
            logger.info(f"Database query result status: {result.get('status')}, Data count: {len(result.get('data', []))}")

            # DEBUG: Log the actual query and first few results
            logger.info(f"EXECUTED QUERY: {query}")
            logger.info(f"QUERY PARAMS: {params}")
            if result.get("data"):
                logger.info(f"SAMPLE DB RESULTS (first 3):")
                for i, item in enumerate(result.get("data", [])[:3]):
                    logger.info(f"  Result {i+1}: exporter_name='{item.get('exporter_name')}', total_quantity={item.get('total_quantity')}, shipment_count={item.get('shipment_count')}")
            else:
                logger.warning("NO DATABASE RESULTS FOUND!")

            if result["status"] != "success":
                logger.warning(f"Database query failed: {result.get('message', 'Unknown error')}")
                # Try enhanced LLM fallback
                if self.llm_client:
                    return self._generate_enhanced_supplier_fallback(hs_code, country, chemical_name, limit)
                else:
                    return SupplierLookupResult.error("Database query failed and no LLM fallback available")

            # Transform API data to match expected structure
            transformed_data = []

            for item in result.get("data", []):
                # Handle potential missing or None values
                shipment_count = item.get("shipment_count", 0) or 0
                total_quantity = item.get("total_quantity", 0) or 0
                average_fob = item.get("average_fob", 0) or 0

                # Calculate total export value
                total_export_value = average_fob * total_quantity if average_fob and total_quantity else 0

                transformed_item = {
                    "supplier_name": truncate_text(item.get("exporter_name", ""), 50),
                    "chemical_name": chemical_name,
                    "hs_code": hs_code,
                    "country": country,
                    "export_data": {
                        "transaction_count": shipment_count,
                        "export_volume_tons": total_quantity,
                        "avg_price_per_ton": average_fob,
                        "total_export_value": total_export_value,
                        "unit": item.get("global_std_unit_id", "KGS")
                    },
                    "metadata": {
                        "from_llm": False,
                        "data_source": "database"
                    }
                }

                transformed_data.append(transformed_item)

            # If no data was found, try to research data using LLM
            if not transformed_data:
                logger.info(f"No data found in database for {chemical_name}, {hs_code}, {country}")
                if self.llm_client:
                    logger.info(f"LLM client available: {self.llm_client is not None}")
                    logger.info(f"Perplexity client available: {self.llm_client.perplexity_client is not None if self.llm_client else False}")
                    logger.info(f"OpenAI client available: {self.llm_client.openai_client is not None if self.llm_client else False}")
                    return self._generate_enhanced_supplier_fallback(hs_code, country, chemical_name, limit)
                else:
                    logger.warning(f"No LLM client available for fallback")
                    return SupplierLookupResult.warning(
                        message=f"No suppliers found for {chemical_name} from {country}. Database returned no results and LLM fallback unavailable.",
                        data=[]
                    )

            # FORCE: Always try to enhance with LLM data to get more suppliers
            logger.info(f"Checking LLM enhancement: llm_client={self.llm_client is not None}")
            if self.llm_client:
                logger.info(f"LLM client details: perplexity={self.llm_client.perplexity_client is not None}, openai={self.llm_client.openai_client is not None}")
                logger.info(f"Enhancing results with LLM data for {chemical_name}, {hs_code}, {country}")
                logger.info(f"Current database results: {len(transformed_data)}, Target limit: {limit}")

                # Request additional suppliers from LLM (always try to get more)
                llm_count = max(limit - len(transformed_data), 5)  # Get at least 5 from LLM
                logger.info(f"Requesting {llm_count} suppliers from LLM")

                llm_result = self._generate_enhanced_supplier_fallback(hs_code, country, chemical_name, llm_count)
                logger.info(f"LLM result status: {llm_result.status}, LLM data count: {len(llm_result.data) if llm_result.data else 0}")

                if llm_result.status == "success" and llm_result.data:
                    # Add LLM suppliers that are not already in database results
                    existing_names = {item["supplier_name"].lower() for item in transformed_data}
                    logger.info(f"Existing supplier names: {existing_names}")

                    added_count = 0
                    for llm_supplier in llm_result.data:
                        supplier_name_lower = llm_supplier["supplier_name"].lower()
                        if supplier_name_lower not in existing_names:
                            transformed_data.append(llm_supplier)
                            added_count += 1
                            logger.info(f"Added LLM supplier: {llm_supplier['supplier_name']}")
                            if len(transformed_data) >= limit:
                                break
                        else:
                            logger.info(f"Skipped duplicate supplier: {llm_supplier['supplier_name']}")

                    logger.info(f"Added {added_count} LLM suppliers to results")
                else:
                    logger.warning(f"LLM fallback failed or returned no data: {llm_result.status}")
            else:
                logger.warning("No LLM client available for enhancement")

            # Enhance supplier names that are empty using LLM
            if self.llm_client:
                for item in transformed_data:
                    if not item["supplier_name"] or item["supplier_name"].strip() == "":
                        # Try to get enhanced supplier information using LLM
                        try:
                            enhanced_result = self.llm_client.generate_supplier_data(chemical_name, hs_code, country, 1)
                            if enhanced_result["status"] == "success" and enhanced_result.get("data"):
                                enhanced_name = enhanced_result["data"][0].get("name", "").strip()
                                if enhanced_name:
                                    item["supplier_name"] = enhanced_name
                                    item["metadata"]["from_llm"] = True
                                    item["metadata"]["data_source"] = "database_enhanced_with_llm"
                        except Exception as e:
                            logger.warning(f"Failed to enhance supplier name: {str(e)}")

            # Final result summary
            total_suppliers = len(transformed_data)
            llm_suppliers = len([item for item in transformed_data if item['metadata']['from_llm']])
            db_suppliers = total_suppliers - llm_suppliers

            logger.info(f"Final result: {total_suppliers} total suppliers ({db_suppliers} from database, {llm_suppliers} from LLM)")

            if llm_suppliers > 0:
                message = f"Data retrieved from database and enhanced with LLM research ({llm_suppliers} LLM-enhanced)"
            else:
                message = "Data retrieved from database"

            return SupplierLookupResult.success(
                data=transformed_data,
                message=message
            )

        except Exception as e:
            logger.error(f"Error getting top suppliers: {str(e)}")
            # Try enhanced LLM fallback on error
            if self.llm_client:
                return self._generate_enhanced_supplier_fallback(hs_code, country, chemical_name, limit)
            else:
                return SupplierLookupResult.error(f"Error getting top suppliers: {str(e)}")

    def get_supplier_details(self, supplier_name: str, hs_code: str, chemical_name: str, country: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific supplier.

        Args:
            supplier_name: Name of the supplier
            hs_code: The HS code
            chemical_name: The name of the chemical
            country: The country

        Returns:
            Dictionary with supplier details
        """
        # Use LLM client to generate comprehensive supplier profile from web research
        if self.llm_client:
            try:
                # Generate detailed profile using Perplexity web search
                result = self.llm_client.generate_detailed_supplier_profile(
                    supplier_name, chemical_name, hs_code, country
                )

                if result.get("status") == "success" and result.get("data"):
                    return result["data"]
                else:
                    # Fallback to basic profile if detailed generation fails
                    return self._generate_basic_supplier_profile(supplier_name, chemical_name, country)

            except Exception as e:
                logger.error(f"Error generating detailed supplier profile: {str(e)}")
                return self._generate_basic_supplier_profile(supplier_name, chemical_name, country)

        # Basic placeholder data when LLM client is not available
        return self._generate_basic_supplier_profile(supplier_name, chemical_name, country)

    def _generate_basic_supplier_profile(self, supplier_name: str, chemical_name: str, country: str) -> Dict[str, Any]:
        """
        Generate a basic supplier profile as fallback.

        Args:
            supplier_name: The name of the supplier
            chemical_name: The name of the chemical
            country: The country of the supplier

        Returns:
            Dictionary with basic supplier information
        """
        return {
            "company_name": supplier_name,
            "overview": f"{supplier_name} is a chemical supplier based in {country} specializing in {chemical_name} and related chemical products.",
            "products": f"Supplier of {chemical_name} and related chemical products for various industrial applications.",
            "manufacturing_capabilities": "",  # Empty string when no data available
            "export_markets": f"Exports to various international markets from {country}.",
            "certifications": [],  # Empty array when no data available
            "competitive_advantages": ["Established supplier", "Quality products", "Reliable delivery"],
            "contact": {
                "website": "",  # Empty string when no data available
                "email": "",   # Empty string when no data available
                "phone": "",   # Empty string when no data available
                "address": f"{country}" if country != "Unknown" else ""
            },
            "additional_info": "Basic profile generated without web research capabilities."
        }

    def _generate_supplier_fallback(self, hs_code: str, country: str, chemical_name: str) -> SupplierLookupResult:
        """
        Research supplier data using LLM as fallback when database data is unavailable.

        Args:
            hs_code: The HS code
            country: The country
            chemical_name: The chemical name

        Returns:
            SupplierLookupResult with researched data or no data if LLM fails to find valid information
        """
        try:
            llm_result = self.llm_client.generate_supplier_data(chemical_name, hs_code, country, 5)

            if llm_result["status"] == "success" and llm_result.get("data"):
                # Only return LLM data if it's valid and contains actual supplier names
                valid_suppliers = []
                for item in llm_result["data"]:
                    supplier_name = item.get("name", "").strip()
                    if supplier_name and supplier_name != "Unknown Supplier":
                        valid_suppliers.append({
                            "name": supplier_name,
                            "country": country,
                            "from_llm": True,
                            # Note: No synthetic transaction data - only supplier names from LLM
                            "transaction_count": None,
                            "export_volume_tons": None,
                            "avg_price_per_ton": None,
                            "total_export_value": None,
                            "unit": "N/A"
                        })

                if valid_suppliers:
                    return SupplierLookupResult.success(
                        data=valid_suppliers,
                        message="Supplier names researched from LLM knowledge base as fallback mechanism (trade data unavailable)"
                    )
                else:
                    return SupplierLookupResult.warning(
                        message=f"No valid suppliers found for {chemical_name} from {country}. LLM could not find reliable supplier information.",
                        data=[]
                    )
            else:
                return SupplierLookupResult.warning(
                    message=f"No valid suppliers found for {chemical_name} from {country}. LLM could not find reliable supplier information.",
                    data=[]
                )
        except Exception as e:
            logger.error(f"Error in LLM fallback: {str(e)}")
            return SupplierLookupResult.warning(
                message=f"No valid suppliers found for {chemical_name} from {country}. Data sources unavailable.",
                data=[]
            )

    def _generate_enhanced_supplier_fallback(self, hs_code: str, country: str, chemical_name: str, limit: int = 10) -> SupplierLookupResult:
        """
        Enhanced supplier fallback that provides data in the expected format with export_data and metadata.

        Args:
            hs_code: The HS code
            country: The country
            chemical_name: The chemical name
            limit: Maximum number of suppliers to return

        Returns:
            SupplierLookupResult with enhanced supplier data in the expected format
        """
        try:
            logger.info(f"Calling LLM generate_supplier_data with: chemical={chemical_name}, hs={hs_code}, country={country}, limit={limit}")
            llm_result = self.llm_client.generate_supplier_data(chemical_name, hs_code, country, limit)
            logger.info(f"LLM result status: {llm_result.get('status')}, Data: {llm_result.get('data', [])}")

            if llm_result["status"] == "success" and llm_result.get("data"):
                # Transform LLM data to match expected format
                enhanced_suppliers = []
                for item in llm_result["data"]:
                    supplier_name = item.get("name", "").strip()
                    if supplier_name and supplier_name != "Unknown Supplier":
                        # Extract enhanced business information with verification data
                        company_type = item.get("company_type", "Data not available")
                        business_focus = item.get("business_focus", "Data not available")
                        market_presence = item.get("market_presence", "Data not available")
                        export_capability = item.get("export_capability", "Data not available")
                        verification_sources = item.get("verification_sources", "1 source")

                        # Extract export data if available
                        export_data = item.get("export_data", {})

                        enhanced_suppliers.append({
                            "supplier_name": supplier_name,
                            "chemical_name": chemical_name,
                            "hs_code": hs_code,
                            "country": country,
                            "export_data": {
                                "transaction_count": export_data.get("annual_volume", ""),
                                "export_volume_tons": export_data.get("annual_volume", ""),
                                "avg_price_per_ton": export_data.get("price_range", ""),
                                "total_export_value": "",
                                "unit": "",
                                "key_markets": export_data.get("key_markets", ""),
                                "shipment_frequency": export_data.get("shipment_frequency", "")
                            },
                            "metadata": {
                                "from_llm": True,
                                "data_source": item.get("data_source", "llm_research"),
                                "llm_source": item.get("llm_source", "unknown"),
                                "company_type": company_type,
                                "business_focus": business_focus,
                                "market_presence": market_presence,
                                "export_capability": export_capability,
                                "verification_sources": verification_sources
                            }
                        })

                if enhanced_suppliers:
                    return SupplierLookupResult.success(
                        data=enhanced_suppliers,
                        message="Supplier data researched from LLM knowledge base as fallback mechanism (trade data unavailable)"
                    )
                else:
                    return SupplierLookupResult.warning(
                        message=f"No valid suppliers found for {chemical_name} from {country}. LLM could not find reliable supplier information.",
                        data=[]
                    )
            else:
                return SupplierLookupResult.warning(
                    message=f"No valid suppliers found for {chemical_name} from {country}. LLM could not find reliable supplier information.",
                    data=[]
                )
        except Exception as e:
            logger.error(f"Error in enhanced LLM fallback: {str(e)}")
            return SupplierLookupResult.warning(
                message=f"No valid suppliers found for {chemical_name} from {country}. Data sources unavailable.",
                data=[]
            )

    def _build_query_top_suppliers(self, hs_code: str, country: str, destination: str, months: int, chemical_name: Optional[str] = None, limit: int = 10) -> tuple:
        """
        Build query to retrieve top suppliers from a specific country.

        Args:
            hs_code: HS code (first 4 digits)
            country: Origin country code
            destination: Destination country code
            months: Number of months to look back
            chemical_name: Optional chemical name for filtering
            limit: Maximum number of suppliers to return

        Returns:
            Tuple of (query, params)
        """
        # Initialize parameters
        params = [country, destination, months]

        # Build product description condition if chemical name is provided
        product_desc_condition = ""
        if chemical_name:
            # Simple ILIKE condition for chemical name
            product_desc_condition = "AND product_desc ILIKE %s"
            params.append(f"%{chemical_name}%")

        # Add HS code parameter
        params.append(hs_code)

        query = f"""
        WITH filtered_by_hs AS (
            SELECT
                exporter_name,
                global_exporter_id,
                global_std_unit_id,
                std_qty,
                fob_value_usd,
                date,
                product_desc,
                gross_wt,
                hs_code
            FROM
                volza_trade_data
            WHERE
                parent_coo = %s
                AND parent_cod = %s
                AND date >= CURRENT_DATE - INTERVAL '%s month'
                {product_desc_condition}
                AND SUBSTRING(hs_code, 1, 4) = %s
        ),
        aggregated AS (
            SELECT
                exporter_name,
                global_exporter_id,
                global_std_unit_id,
                SUM(std_qty) AS total_quantity,
                AVG(fob_value_usd) AS average_fob,
                MIN(fob_value_usd) AS minimum_fob,
                MAX(fob_value_usd) AS maximum_fob,
                COUNT(*) AS shipment_count,
                AVG(std_qty) AS average_quantity_per_shipment,
                SUM(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE 0 END) AS total_gross_weight,
                AVG(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE NULL END) AS average_gross_weight
            FROM
                filtered_by_hs
            WHERE
                CASE
                    WHEN gross_wt ~ '^[0-9]+$' AND std_qty IS NOT NULL THEN
                        GREATEST(gross_wt::INTEGER, std_qty) >= 10
                    WHEN gross_wt ~ '^[0-9]+$' THEN
                        gross_wt::INTEGER >= 10
                    WHEN std_qty IS NOT NULL THEN
                        std_qty >= 10
                    ELSE FALSE
                END
            GROUP BY
                exporter_name, global_exporter_id, global_std_unit_id
            HAVING
                COUNT(*) >= 1
            ORDER BY
                total_quantity DESC
        )

        SELECT *
        FROM aggregated
        ORDER BY total_quantity DESC
        LIMIT {limit};
        """

        return query, tuple(params)


# Initialize the integrated services
integrated_country_service = IntegratedCountryService(db_client, llm_client)
integrated_supplier_service = IntegratedSupplierService(db_client, llm_client)

ns = Namespace(name='trade-finder', path="/v1/trade", description='Trade Data Analysis APIs')

# Input/Output Models

trade_data_by_country_query_params = reqparse.RequestParser()
trade_data_by_country_query_params.add_argument('hs_code', type=str, required=True, help='HS code is required')
trade_data_by_country_query_params.add_argument('origin', type=str, required=True, help='Origin country')
trade_data_by_country_query_params.add_argument('destination', type=str, required=True, help='Destination country')
trade_data_by_country_query_params.add_argument('months', type=int, default=12, help='Last k months to consider')

top_suppliers_query_params = reqparse.RequestParser()
top_suppliers_query_params.add_argument('hs_code', type=str, required=True, help='HS code is required')
top_suppliers_query_params.add_argument('origin', type=str, required=True, help='Origin country')
top_suppliers_query_params.add_argument('destination', type=str, default='United States', help='Destination country')
top_suppliers_query_params.add_argument('months', type=int, default=12, help='Last k months to consider')
# ORIGINAL: No chemical_name parameter
# MODIFIED: Added chemical_name parameter from volza_query_engine
top_suppliers_query_params.add_argument('chemical_name', type=str, required=False, help='Name of the chemical')

top_supplier_by_geography_query_params = reqparse.RequestParser()
top_supplier_by_geography_query_params.add_argument('hs_code', type=str, required=True, help='HS code is required')
top_supplier_by_geography_query_params.add_argument('months', type=int, default=12, help='Last k months to consider')
top_supplier_by_geography_query_params.add_argument('destination', type=str, default='United States', help='Destination country')
top_supplier_by_geography_query_params.add_argument('chemical_name', type=str, required=False, help='Name of the chemical')

# Response Models
query_info = api.model('QueryInfo', {
    'hs_code': fields.String,
    'origin': fields.String,
    'destination': fields.String,
    'months': fields.Integer,
})

response_model = api.model('Response', {
    'success': fields.Boolean,
    'query': fields.Nested(query_info),
    'result': fields.Raw,
})

# Enhanced API models for new country endpoints
country_lookup_input = api.model('CountryLookupInput', {
    'hs_code': fields.String(required=True, description='HS Code'),
    'chemical_name': fields.String(required=True, description='Chemical Name'),
    'destination': fields.String(required=True, description='Destination Country'),
    'months': fields.Integer(required=False, default=12, description='Number of months of data')
})

country_lookup_output = api.model('CountryLookupOutput', {
    'status': fields.String(description='Status of the lookup'),
    'data': fields.List(fields.Raw, description='Country trade data'),
    'message': fields.String(description='Additional message', required=False)
})

ranking_input = api.model('RankingInput', {
    'countries_data': fields.List(fields.Raw, required=True, description='List of country data to rank'),
    'top_n': fields.Integer(required=False, default=10, description='Number of top countries to return')
})

# Enhanced Supplier API models
supplier_lookup_input = api.model('SupplierLookupInput', {
    'hs_code': fields.String(required=True, description='HS Code'),
    'country': fields.String(required=True, description='Origin Country'),
    'destination': fields.String(required=True, description='Destination Country'),
    'chemical_name': fields.String(required=True, description='Chemical Name'),
    'months': fields.Integer(required=False, default=12, description='Number of months of data'),
    'limit': fields.Integer(required=False, default=10, description='Maximum number of suppliers to return')
})

supplier_lookup_output = api.model('SupplierLookupOutput', {
    'status': fields.String(description='Status of the lookup'),
    'data': fields.List(fields.Raw, description='Supplier trade data'),
    'message': fields.String(description='Additional message', required=False)
})

supplier_details_input = api.model('SupplierDetailsInput', {
    'supplier_name': fields.String(required=True, description='Supplier Name'),
    'hs_code': fields.String(required=True, description='HS Code'),
    'chemical_name': fields.String(required=True, description='Chemical Name'),
    'country': fields.String(required=True, description='Country')
})

supplier_details_output = api.model('SupplierDetailsOutput', {
    'company_name': fields.String(description='Company name'),
    'overview': fields.String(description='Company overview'),
    'products': fields.String(description='Product portfolio and focus areas'),
    'manufacturing_capabilities': fields.String(description='Manufacturing facilities and capabilities'),
    'export_markets': fields.String(description='Export markets and global reach'),
    'certifications': fields.Raw(description='Certifications and quality standards'),
    'competitive_advantages': fields.Raw(description='Key competitive advantages'),
    'contact': fields.Raw(description='Contact information including website, email, phone, address'),
    'additional_info': fields.String(description='Additional relevant information about the company'),
    'verification_status': fields.String(description='Data verification status'),
    'data_sources': fields.Raw(description='Sources of the data')
})

supplier_ranking_input = api.model('SupplierRankingInput', {
    'suppliers_data': fields.List(fields.Raw, required=True, description='List of supplier data to rank'),
    'top_n': fields.Integer(required=False, default=10, description='Number of top suppliers to return')
})

# New comprehensive supplier intelligence input model
supplier_intelligence_input = api.model('SupplierIntelligenceInput', {
    'supplier_name': fields.String(required=True, description='Name of the supplier to research'),
    'chemical_name': fields.String(required=False, description='Chemical name (optional - helps with context)'),
    'hs_code': fields.String(required=False, description='HS code (optional - helps with context)'),
    'country': fields.String(required=False, description='Country (optional - will be auto-detected if not provided)')
})

# Comprehensive supplier intelligence output model
supplier_intelligence_output = api.model('SupplierIntelligenceOutput', {
    'status': fields.String(description='Status of the intelligence gathering'),
    'data': fields.Nested(supplier_details_output, description='Comprehensive supplier profile'),
    'message': fields.String(description='Additional information about the research process'),
    'sources': fields.List(fields.String, description='Data sources used for intelligence gathering')
})

@ns.route('/top-suppliers-by-geography')
class TopSuppliersByGeography(Resource):
    @ns.expect(top_supplier_by_geography_query_params)
    @ns.marshal_with(response_model)
    def get(self):
        """Get top geographies supplying products"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /top-suppliers-by-geography")# from IP: {client_ip}")

        try:
            args = top_supplier_by_geography_query_params.parse_args()
            logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")
            result = TradeFinderService().get_top_suppliers_by_geography(
                args['hs_code'],
                args.get('months', 12),
                args.get('destination', 'United States'),
                args.get('chemical_name')
            )

            result = json.loads(json.dumps(result, indent=4, sort_keys=True, default=str))
            logger.info(f"Request [ID: {request_id}] - Successful response with {result}")

            return {
                'success': True,
                'query': args,
                'result': result
            }

        except Exception as e:
            print(f"Error in top_suppliers_by_geography: {e}")
            api.abort(500, f"Internal server error: {str(e)}")

# ORIGINAL: Not present in original code
# MODIFIED: Added from volza_query_engine to support tariff data
@ns.route('/top-suppliers-by-geography-tariff')
class TopSuppliersByGeographyTariff(Resource):
    @ns.expect(top_supplier_by_geography_query_params)
    @ns.marshal_with(response_model)
    def get(self):
        """Get top geographies supplying products with tariff data"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /top-suppliers-by-geography-tariff")

        try:
            args = top_supplier_by_geography_query_params.parse_args()
            logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")
            result = TradeFinderService().get_top_suppliers_by_geography_tariff(
                args['hs_code'],
                args.get('months', 12),
                args.get('destination', 'United States'),
                args.get('chemical_name')
            )

            result = json.loads(json.dumps(result, indent=4, sort_keys=True, default=str))
            logger.info(f"Request [ID: {request_id}] - Successful response with {result}")

            return {
                'success': True,
                'query': args,
                'result': result
            }

        except Exception as e:
            print(f"Error in top_suppliers_by_geography_tariff: {e}")
            api.abort(500, f"Internal server error: {str(e)}")

@ns.route('/trade-data-by-country')
class TradeDataByCountry(Resource):
    @ns.expect(trade_data_by_country_query_params)
    @ns.marshal_with(response_model)
    def get(self):
        """Get trade data for a specific HS code, origin, and destination"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /trade-data-by-country")# from IP: {client_ip}")

        try:
            args = trade_data_by_country_query_params.parse_args()
            logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")
            result = TradeFinderService().get_trade_data_by_country(
                args['hs_code'],
                args['origin'],
                args['destination'],
                args['months']
            )

            logger.info(f"Request [ID: {request_id}] - Successful response with {result}")
            result = json.loads(json.dumps(result, indent=4, sort_keys=True, default=str))

            print("found result", result)

            return {
                'success': True,
                'query': args,
                'result': result
            }

        except Exception as e:
            print(f"Error in trade_data_by_country: {e}")
            api.abort(500, f"Internal server error: {str(e)}")

@ns.route('/top-suppliers')
class TopSuppliers(Resource):
    @ns.expect(top_suppliers_query_params)
    @ns.marshal_with(response_model)
    def get(self):
        """Get top suppliers for a specific HS code, origin, and destination with optional chemical name filtering"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /top-suppliers")# from IP: {client_ip}")

        try:
            args = top_suppliers_query_params.parse_args()
            logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")

            # ORIGINAL: Did not pass chemical_name parameter
            # MODIFIED: Added chemical_name parameter from volza_query_engine
            result = TradeFinderService().get_top_suppliers(
                args['hs_code'],
                args['origin'],
                args['destination'],
                args['months'],
                args.get('chemical_name')
            )

            result = json.loads(json.dumps(result, indent=4, sort_keys=True, default=str))
            logger.info(f"Request [ID: {request_id}] - Successful response with {result}")

            return {
                'success': True,
                'query': args,
                'result': result
            }

        except Exception as e:
            print(f"Error in top_suppliers: {e}")
            api.abort(500, f"Internal server error: {str(e)}")


# Enhanced Country Endpoints with LLM Fallbacks
@ns.route('/top-exporters')
class TopExporters(Resource):
    @ns.expect(country_lookup_input, validate=True)
    @ns.marshal_with(country_lookup_output)
    def post(self):
        """Enhanced endpoint: Get top exporting countries for a chemical with LLM fallbacks"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /trade/top-exporters")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Validate inputs
        hs_code = data.get('hs_code', '')
        chemical_name = data.get('chemical_name', '')
        destination = data.get('destination', '')
        months = data.get('months', 12)

        # Validate HS code
        is_valid, error_message = validate_hs_code(hs_code)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - HS code validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": []
            }, 400

        # Validate destination country
        is_valid, error_message = validate_country(destination)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Country validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": []
            }, 400

        # Validate months
        is_valid, error_message = validate_months(months)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Months validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": []
            }, 400

        # Sanitize inputs
        hs_code = sanitize_input(hs_code)
        chemical_name = sanitize_input(chemical_name)
        destination = sanitize_input(destination)

        try:
            # Call the integrated country service
            result = integrated_country_service.get_top_countries(hs_code, chemical_name, destination, months)

            logger.info(f"Request [ID: {request_id}] - Successful response with status: {result.status}")
            return result.to_dict()

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting top exporters: {str(e)}",
                "data": []
            }, 500


@ns.route('/top-exporters-with-tariff')
class TopExportersWithTariff(Resource):
    @ns.expect(country_lookup_input, validate=True)
    @ns.marshal_with(country_lookup_output)
    def post(self):
        """Enhanced endpoint: Get top exporting countries with tariff information and LLM fallbacks"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /trade/top-exporters-with-tariff")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Validate inputs (same validation as above)
        hs_code = data.get('hs_code', '')
        chemical_name = data.get('chemical_name', '')
        destination = data.get('destination', '')
        months = data.get('months', 12)

        # Validate HS code
        is_valid, error_message = validate_hs_code(hs_code)
        if not is_valid:
            return {"status": "error", "message": error_message, "data": []}, 400

        # Validate destination country
        is_valid, error_message = validate_country(destination)
        if not is_valid:
            return {"status": "error", "message": error_message, "data": []}, 400

        # Validate months
        is_valid, error_message = validate_months(months)
        if not is_valid:
            return {"status": "error", "message": error_message, "data": []}, 400

        # Sanitize inputs
        hs_code = sanitize_input(hs_code)
        chemical_name = sanitize_input(chemical_name)
        destination = sanitize_input(destination)

        try:
            # Call the integrated country service with tariff data
            result = integrated_country_service.get_top_countries_with_tariff(hs_code, chemical_name, destination, months)

            logger.info(f"Request [ID: {request_id}] - Successful response with status: {result.status}")
            return result.to_dict()

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting top exporters with tariff: {str(e)}",
                "data": []
            }, 500


@ns.route('/rank-countries')
class RankCountries(Resource):
    @ns.expect(ranking_input, validate=True)
    @ns.marshal_with(country_lookup_output)
    def post(self):
        """Enhanced endpoint: Rank countries based on composite scoring"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /trade/rank-countries")
        data = request.get_json()

        countries_data = data.get('countries_data', [])
        top_n = data.get('top_n', 10)

        if not countries_data:
            return {
                "status": "error",
                "message": "No countries data provided",
                "data": []
            }, 400

        try:
            # Use the enhanced ranking service
            ranked_countries = ranking_service.rank_countries(countries_data, top_n)

            logger.info(f"Request [ID: {request_id}] - Successfully ranked {len(ranked_countries)} countries")
            return {
                "status": "success",
                "data": ranked_countries,
                "message": f"Ranked top {len(ranked_countries)} countries"
            }

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error ranking countries: {str(e)}",
                "data": []
            }, 500


# Enhanced Supplier Endpoints with LLM Fallbacks
@ns.route('/debug-services')
class DebugServices(Resource):
    def get(self):
        """Debug endpoint to check service availability"""
        try:
            debug_info = {
                "db_client_available": db_client is not None,
                "openai_client_available": openai_client is not None,
                "perplexity_client_available": perplexity_client is not None,
                "llm_client_available": llm_client is not None,
                "integrated_supplier_service_available": integrated_supplier_service is not None,
                "llm_client_has_perplexity": llm_client.perplexity_client is not None if llm_client else False,
                "llm_client_has_openai": llm_client.openai_client is not None if llm_client else False,
            }

            # Test database connection
            try:
                test_query = "SELECT 1 as test"
                db_result = db_client.execute_query(test_query, [])
                debug_info["db_connection_test"] = db_result.get("status") == "success"
            except Exception as e:
                debug_info["db_connection_test"] = f"Error: {str(e)}"

            # Test LLM supplier generation
            if llm_client:
                try:
                    test_result = llm_client.generate_supplier_data("Epoxide Resin", "3907.30", "India", 3)
                    debug_info["llm_test_status"] = test_result.get("status")
                    debug_info["llm_test_data_count"] = len(test_result.get("data", []))
                except Exception as e:
                    debug_info["llm_test_error"] = str(e)

            return debug_info
        except Exception as e:
            return {"error": str(e)}, 500

# Debug endpoints removed for production

@ns.route('/enhanced-top-suppliers')
class EnhancedTopSuppliers(Resource):
    @ns.expect(supplier_lookup_input, validate=True)
    @ns.marshal_with(supplier_lookup_output)
    def post(self):
        """Enhanced endpoint: Get top suppliers from a specific country with LLM fallbacks"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /trade/enhanced-top-suppliers")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Validate inputs
        hs_code = data.get('hs_code', '')
        country = data.get('country', '')
        destination = data.get('destination', '')
        chemical_name = data.get('chemical_name', '')
        months = data.get('months', 12)
        limit = data.get('limit', 10)  # Default to 10 suppliers

        # Validate HS code
        is_valid, error_message = validate_hs_code(hs_code)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - HS code validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": []
            }, 400

        # Validate origin country
        is_valid, error_message = validate_country(country)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Origin country validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": []
            }, 400

        # Validate destination country
        is_valid, error_message = validate_country(destination)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Destination country validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": []
            }, 400

        # Validate months
        is_valid, error_message = validate_months(months)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Months validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": []
            }, 400

        # Sanitize inputs
        hs_code = sanitize_input(hs_code)
        country = sanitize_input(country)
        destination = sanitize_input(destination)
        chemical_name = sanitize_input(chemical_name)

        try:
            # Debug: Check if integrated_supplier_service is available
            logger.info(f"Request [ID: {request_id}] - Integrated supplier service available: {integrated_supplier_service is not None}")
            logger.info(f"Request [ID: {request_id}] - LLM client in service: {integrated_supplier_service.llm_client is not None if integrated_supplier_service else False}")

            # Call the integrated supplier service with limit parameter
            result = integrated_supplier_service.get_top_suppliers(hs_code, country, destination, chemical_name, months, limit)

            logger.info(f"Request [ID: {request_id}] - Service returned result with status: {result.status}")
            logger.info(f"Request [ID: {request_id}] - Result data count: {len(result.data) if result.data else 0}")
            logger.info(f"Request [ID: {request_id}] - Result message: {result.message}")

            return result.to_dict()

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting top suppliers: {str(e)}",
                "data": []
            }, 500


@ns.route('/supplier-intelligence')
class SupplierIntelligence(Resource):
    @ns.expect(supplier_intelligence_input, validate=True)
    @ns.marshal_with(supplier_intelligence_output)
    def post(self):
        """Comprehensive supplier intelligence endpoint: Get detailed supplier information from web research"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /trade/supplier-intelligence")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Extract inputs
        supplier_name = data.get('supplier_name', '').strip()
        chemical_name = data.get('chemical_name', '').strip()
        hs_code = data.get('hs_code', '').strip()
        country = data.get('country', '').strip()

        # Validate supplier name (required)
        if not supplier_name:
            return {
                "status": "error",
                "message": "Supplier name is required",
                "data": {},
                "sources": []
            }, 400

        # Sanitize inputs
        supplier_name = sanitize_input(supplier_name)
        chemical_name = sanitize_input(chemical_name) if chemical_name else None
        hs_code = sanitize_input(hs_code) if hs_code else None
        country = sanitize_input(country) if country else None

        # Validate optional inputs if provided
        if hs_code:
            is_valid, error_message = validate_hs_code(hs_code)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - HS code validation failed: {error_message}")
                return {
                    "status": "error",
                    "message": f"Invalid HS code: {error_message}",
                    "data": {},
                    "sources": []
                }, 400

        if country:
            is_valid, error_message = validate_country(country)
            if not is_valid:
                logger.warning(f"Request [ID: {request_id}] - Country validation failed: {error_message}")
                return {
                    "status": "error",
                    "message": f"Invalid country: {error_message}",
                    "data": {},
                    "sources": []
                }, 400

        try:
            # Set defaults if not available
            if not country:
                country = "Unknown"
            if not chemical_name:
                chemical_name = "Chemical products"
            if not hs_code:
                hs_code = "0000"

            # Generate comprehensive supplier profile using LLM
            if llm_client:
                result = llm_client.generate_detailed_supplier_profile(
                    supplier_name, chemical_name, hs_code, country
                )

                if result.get("status") == "success" and result.get("data"):
                    sources = ["Perplexity Web Search", "Chemical Industry Intelligence"]

                    logger.info(f"Request [ID: {request_id}] - Successfully generated comprehensive supplier profile")
                    return {
                        "status": "success",
                        "data": result["data"],
                        "message": "Comprehensive supplier intelligence gathered from web research",
                        "sources": sources
                    }
                else:
                    # Fallback to basic profile
                    basic_profile = integrated_supplier_service._generate_basic_supplier_profile(
                        supplier_name, chemical_name, country
                    )

                    logger.warning(f"Request [ID: {request_id}] - Fallback to basic profile")
                    return {
                        "status": "warning",
                        "data": basic_profile,
                        "message": "Basic supplier profile generated (web research unavailable)",
                        "sources": ["Internal Database"]
                    }
            else:
                # LLM client not available
                basic_profile = integrated_supplier_service._generate_basic_supplier_profile(
                    supplier_name, chemical_name, country
                )

                logger.warning(f"Request [ID: {request_id}] - LLM client not available")
                return {
                    "status": "warning",
                    "data": basic_profile,
                    "message": "Basic supplier profile generated (intelligence services unavailable)",
                    "sources": ["Internal Database"]
                }

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error gathering supplier intelligence: {str(e)}",
                "data": {},
                "sources": []
            }, 500


@ns.route('/supplier-details')
class SupplierDetails(Resource):
    @ns.expect(supplier_details_input, validate=True)
    @ns.marshal_with(supplier_details_output)
    def post(self):
        """Enhanced endpoint: Get detailed information about a specific supplier"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /trade/supplier-details")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Validate inputs
        supplier_name = data.get('supplier_name', '')
        hs_code = data.get('hs_code', '')
        chemical_name = data.get('chemical_name', '')
        country = data.get('country', '')

        # Validate supplier name
        is_valid, error_message = validate_supplier_name(supplier_name)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Supplier name validation failed: {error_message}")
            return {
                "error": error_message
            }, 400

        # Validate HS code
        is_valid, error_message = validate_hs_code(hs_code)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - HS code validation failed: {error_message}")
            return {
                "error": error_message
            }, 400

        # Validate country
        is_valid, error_message = validate_country(country)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Country validation failed: {error_message}")
            return {
                "error": error_message
            }, 400

        # Sanitize inputs
        supplier_name = sanitize_input(supplier_name)
        hs_code = sanitize_input(hs_code)
        chemical_name = sanitize_input(chemical_name)
        country = sanitize_input(country)

        try:
            # Call the integrated supplier service
            result = integrated_supplier_service.get_supplier_details(supplier_name, hs_code, chemical_name, country)

            logger.info(f"Request [ID: {request_id}] - Successful response")
            return result

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "error": f"Error getting supplier details: {str(e)}"
            }, 500


@ns.route('/rank-suppliers')
class RankSuppliers(Resource):
    @ns.expect(supplier_ranking_input, validate=True)
    @ns.marshal_with(supplier_lookup_output)
    def post(self):
        """Enhanced endpoint: Rank suppliers based on composite scoring"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /trade/rank-suppliers")
        data = request.get_json()

        suppliers_data = data.get('suppliers_data', [])
        top_n = data.get('top_n', 10)

        if not suppliers_data:
            return {
                "status": "error",
                "message": "No suppliers data provided",
                "data": []
            }, 400

        try:
            # Use the enhanced ranking service
            ranked_suppliers = ranking_service.rank_suppliers(suppliers_data, top_n)

            logger.info(f"Request [ID: {request_id}] - Successfully ranked {len(ranked_suppliers)} suppliers")
            return {
                "status": "success",
                "data": ranked_suppliers,
                "message": f"Ranked top {len(ranked_suppliers)} suppliers"
            }

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error ranking suppliers: {str(e)}",
                "data": []
            }, 500


# Chemical Lookup Endpoints
chemical_lookup_input = api.model('ChemicalLookupInput', {
    'chemical_name': fields.String(required=True, description='Name of the chemical'),
    'application': fields.String(required=True, description='Application of the chemical'),
    'category': fields.String(required=False, description='Category of the chemical (optional)')
})

chemical_lookup_output = api.model('ChemicalLookupOutput', {
    'status': fields.String(description='Status of the request'),
    'message': fields.String(description='Response message'),
    'data': fields.Raw(description='Chemical information and HS code data')
})

chemical_info_input = api.model('ChemicalInfoInput', {
    'chemical_name': fields.String(required=True, description='Name of the chemical'),
    'hs_code': fields.String(required=False, description='HS code (optional)')
})

chemical_info_output = api.model('ChemicalInfoOutput', {
    'status': fields.String(description='Status of the request'),
    'message': fields.String(description='Response message'),
    'data': fields.Raw(description='Detailed chemical information')
})


@ns.route('/chemical-lookup')
class ChemicalLookup(Resource):
    @ns.expect(chemical_lookup_input, validate=True)
    @ns.marshal_with(chemical_lookup_output)
    def post(self):
        """Get HS codes for a chemical and its application"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /trade/chemical-lookup")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Extract and validate inputs
        chemical_name = data.get('chemical_name', '')
        application = data.get('application', '')
        category = data.get('category', '')

        # Validate chemical input
        is_valid, error_message = validate_chemical_input(chemical_name, application)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Chemical validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": {}
            }, 400

        # Sanitize inputs
        chemical_name = sanitize_input(chemical_name)
        application = sanitize_input(application)
        if category:
            category = sanitize_input(category)

        # If category is provided but application is not, use category as application
        if not application and category:
            application = category

        try:
            # Use LLM client to get chemical information
            if llm_client:
                result = llm_client.get_chemical_info(chemical_name, application)

                if result and "hs_code" in result:
                    # Format the response with additional formatting
                    formatted_result = {
                        "chemical_name": truncate_text(chemical_name, 100),
                        "application": truncate_text(application, 100),
                        "hs_code": result.get("hs_code", ""),
                        "description": result.get("description", ""),
                        "category": result.get("category", ""),
                        "formatted_chemical_name": truncate_text(chemical_name, 50),
                        "formatted_application": truncate_text(application, 50)
                    }

                    logger.info(f"Request [ID: {request_id}] - Successfully found HS code: {result.get('hs_code', '')}")
                    return {
                        "status": "success",
                        "message": "Chemical information retrieved successfully",
                        "data": formatted_result
                    }
                else:
                    logger.warning(f"Request [ID: {request_id}] - No HS code found")
                    return {
                        "status": "warning",
                        "message": "No HS code found for the given chemical and application",
                        "data": {}
                    }
            else:
                logger.error(f"Request [ID: {request_id}] - LLM client not available")
                return {
                    "status": "error",
                    "message": "Chemical lookup service not available",
                    "data": {}
                }, 503

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error looking up chemical: {str(e)}",
                "data": {}
            }, 500


@ns.route('/chemical-info')
class ChemicalInfo(Resource):
    @ns.expect(chemical_info_input, validate=True)
    @ns.marshal_with(chemical_info_output)
    def post(self):
        """Get detailed information about a chemical"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /trade/chemical-info")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Extract inputs
        chemical_name = data.get('chemical_name', '')
        hs_code = data.get('hs_code', '')

        # Validate chemical name
        if not chemical_name or not chemical_name.strip():
            return {
                "status": "error",
                "message": "Chemical name is required",
                "data": {}
            }, 400

        # Sanitize inputs
        chemical_name = sanitize_input(chemical_name)
        if hs_code:
            hs_code = sanitize_input(hs_code)

        try:
            # Use LLM client to get detailed chemical information
            if llm_client:
                # Get comprehensive chemical information
                result = llm_client.get_detailed_chemical_info(chemical_name, hs_code)

                if result:
                    # Format the response
                    formatted_result = {
                        "chemical_name": truncate_text(chemical_name, 100),
                        "hs_code": result.get("hs_code", hs_code),
                        "description": result.get("description", ""),
                        "properties": result.get("properties", {}),
                        "applications": format_list_display(result.get("applications", []), 5),
                        "safety_info": result.get("safety_info", {}),
                        "trade_info": result.get("trade_info", {}),
                        "formatted_chemical_name": truncate_text(chemical_name, 50)
                    }

                    logger.info(f"Request [ID: {request_id}] - Successfully retrieved chemical info")
                    return {
                        "status": "success",
                        "message": "Chemical information retrieved successfully",
                        "data": formatted_result
                    }
                else:
                    logger.warning(f"Request [ID: {request_id}] - No information found")
                    return {
                        "status": "warning",
                        "message": "No detailed information found for the given chemical",
                        "data": {}
                    }
            else:
                logger.error(f"Request [ID: {request_id}] - LLM client not available")
                return {
                    "status": "error",
                    "message": "Chemical information service not available",
                    "data": {}
                }, 503

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting chemical information: {str(e)}",
                "data": {}
            }, 500


api.add_namespace(ns)




