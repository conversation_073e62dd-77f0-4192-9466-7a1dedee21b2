"""Controller for resolving chemical codes and related information."""

import datetime
import json
import logging
import os
import pandas as pd
import re
from typing import Dict, Any, Optional, <PERSON>ple

from flask import request
from flask_restx import Namespace, Resource, fields, reqparse

from app import api
from app.models.chemical import ChemicalLookupResult, ChemicalMatchResult
from app.services.openai_client import OpenAIClient
from app.services.perplexity_client import PerplexityClient

logger = logging.getLogger(__name__)

# ============================================================================
# INTEGRATED VALIDATION FUNCTIONS
# ============================================================================

def validate_chemical_input(chemical_name: str, application: str) -> Tuple[bool, Optional[str]]:
    """
    Validate chemical input parameters.

    Args:
        chemical_name: Name of the chemical
        application: Application of the chemical

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not chemical_name or not chemical_name.strip():
        return False, "Chemical name is required"

    if not application or not application.strip():
        return False, "Chemical application is required"

    # Check for potentially dangerous inputs (SQL injection, etc.)
    dangerous_patterns = [
        r';\s*DROP',
        r';\s*DELETE',
        r';\s*UPDATE',
        r';\s*INSERT',
        r'--',
        r'/\*'
    ]

    for pattern in dangerous_patterns:
        if re.search(pattern, chemical_name, re.IGNORECASE) or re.search(pattern, application, re.IGNORECASE):
            return False, "Invalid input detected"

    return True, None

def sanitize_input(input_str: str) -> str:
    """
    Sanitize input string to prevent injection attacks.

    Args:
        input_str: Input string to sanitize

    Returns:
        Sanitized string
    """
    if not input_str:
        return ""

    # Remove potentially dangerous characters
    sanitized = re.sub(r'[;\'"]', '', input_str)

    # Limit length
    return sanitized[:500]

# ============================================================================
# INTEGRATED LLM CLIENT
# ============================================================================

class LLMClient:
    """
    Orchestrates dual LLM verification using OpenAI and Perplexity clients.
    """

    def __init__(self, openai_client=None, perplexity_client=None):
        """
        Initialize the LLM client.

        Args:
            openai_client: OpenAI client instance
            perplexity_client: Perplexity client instance
        """
        self.openai_client = openai_client
        self.perplexity_client = perplexity_client

    def get_chemical_info(self, chemical_name: str, chemical_application: str) -> Dict[str, Any]:
        """
        Get chemical information using OpenAI.

        Args:
            chemical_name: Name of the chemical
            chemical_application: Application of the chemical

        Returns:
            Dictionary with chemical information
        """
        if not self.openai_client:
            logger.warning("OpenAI client not initialized. Returning default response.")
            return {
                "product_name": chemical_name,
                "product_family": "Not found",
                "cas_number": "Not found",
                "hs_code": "Not found",
                "hts_number": "Not found",
                "product_application": chemical_application + " (user provided)"
            }

        try:
            # Prepare system prompt for OpenAI
            system_prompt = """You are a helpful assistant specializing in chemical information.
            When provided with a chemical name, and its application, return a JSON object with the product_name,
            product_family (chemical family/category), cas_number (CAS Registry Number), hs_code (8-digit hs code), hts_number (10-digit HTS code)
            and product_application (other common uses).
            In case of multiple hs codes, return only the most specific one.
            Format your response as valid JSON only with no additional text."""

            user_prompt = f"Chemical name: {chemical_name}\nApplication: {chemical_application}"

            # Create messages for OpenAI
            from langchain_core.messages import SystemMessage, HumanMessage
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            # Query OpenAI
            response = self.openai_client.query(messages)

            # Try to parse JSON response
            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                logger.error(f"Failed to parse OpenAI response as JSON: {response}")
                return {
                    "product_name": chemical_name,
                    "product_family": "Error",
                    "cas_number": "Error",
                    "hs_code": "Error",
                    "hts_number": "Error",
                    "product_application": "Error"
                }

        except Exception as e:
            logger.error(f"Error querying OpenAI: {str(e)}")
            return {
                "product_name": chemical_name,
                "product_family": "Error",
                "cas_number": "Error",
                "hs_code": "Error",
                "hts_number": "Error",
                "product_application": "Error"
            }

    def query_perplexity(self, chemical_name: str, chemical_application: str) -> Dict[str, Any]:
        """
        Query Perplexity for chemical information.

        Args:
            chemical_name: Name of the chemical
            chemical_application: Application of the chemical

        Returns:
            Dictionary with chemical information
        """
        if not self.perplexity_client:
            logger.warning("Perplexity client not initialized. Returning default response.")
            return {
                "product_name": chemical_name,
                "product_family": "Not found",
                "cas_number": "Not found",
                "hs_code": "Not found",
                "hts_number": "Not found",
                "product_application": chemical_application + " (user provided)"
            }

        try:
            # Prepare prompt for Perplexity
            prompt = f"""You are a helpful assistant specializing in chemical information.
            When provided with a chemical name '{chemical_name}' and its application '{chemical_application}', return a JSON object with the product_name,
            product_family (chemical family/category), cas_number (CAS Registry Number), hs_code (8-digit hs code), hts_number (10-digit HTS code)
            and product_application (other common uses).
            In case of multiple hs codes, return only the most specific one.
            Format your response as valid JSON only with no additional text."""

            payload = {
                "model": "sonar-pro",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a specialized chemical information expert that produces JSON responses. Return ONLY valid JSON with no additional explanations."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.2
            }

            # Query Perplexity
            response = self.perplexity_client.query(payload)

            # Extract content from response
            if response and "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]

                # Try to parse JSON response
                try:
                    result = json.loads(content)
                    return result
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse Perplexity response as JSON: {content}")
                    return {
                        "product_name": chemical_name,
                        "product_family": "Error",
                        "cas_number": "Error",
                        "hs_code": "Error",
                        "hts_number": "Error",
                        "product_application": "Error"
                    }
            else:
                logger.error("No valid response from Perplexity")
                return {
                    "product_name": chemical_name,
                    "product_family": "Error",
                    "cas_number": "Error",
                    "hs_code": "Error",
                    "hts_number": "Error",
                    "product_application": "Error"
                }

        except Exception as e:
            logger.error(f"Error querying Perplexity: {str(e)}")
            return {
                "product_name": chemical_name,
                "product_family": "Error",
                "cas_number": "Error",
                "hs_code": "Error",
                "hts_number": "Error",
                "product_application": "Error"
            }

# Initialize enhanced services for new endpoints
openai_client = OpenAIClient()
perplexity_client = PerplexityClient()
llm_client = LLMClient(openai_client, perplexity_client)

ns = Namespace(name='code-resolver', path="/v1/code-resolver", description='Intelligent Code Resolution APIs')

# Integrated Chemical Service functionality
class IntegratedChemicalService:
    """
    Integrated chemical service with dual LLM verification and fallback mechanisms.
    """

    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        Initialize the integrated chemical service.

        Args:
            llm_client: LLM client for dual verification
        """
        self.llm_client = llm_client
        self.historical_data = self._load_historical_data()

    def _load_historical_data(self) -> Optional[pd.DataFrame]:
        """
        Load historical chemical data for quick lookups.

        Returns:
            DataFrame with historical data or None if not available
        """
        try:
            # Try to load from the data directory
            data_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'historical_data.csv')
            if os.path.exists(data_path):
                df = pd.read_csv(data_path)
                logger.info(f"Loaded historical data with {len(df)} records")

                # Log column names for debugging
                logger.info(f"Historical data columns: {list(df.columns)}")

                # Log some sample product names for debugging
                if 'product_name' in df.columns:
                    unique_products = df['product_name'].dropna().unique()[:10]  # First 10 unique products
                    logger.info(f"Sample product names: {list(unique_products)}")
                else:
                    logger.warning("'product_name' column not found in historical data!")

                return df
            else:
                logger.info("No historical data file found")
                return None
        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
            return None

    def lookup_chemical(self, chemical_name: str, chemical_application: str) -> ChemicalLookupResult:
        """
        Look up chemical information with dual LLM verification.

        Args:
            chemical_name: Name of the chemical
            chemical_application: Application of the chemical

        Returns:
            ChemicalLookupResult with the lookup results
        """
        try:
            # First, check historical data if available
            if self.historical_data is not None:
                historical_result = self._check_historical_data(chemical_name, chemical_application)
                if historical_result:
                    return ChemicalLookupResult.success(
                        data=[historical_result],
                        message="Found in historical data"
                    )

            # If not found in historical data, use LLM services
            if self.llm_client:
                logger.info(f"Querying LLM services for {chemical_name}...")
                result = self._compute_catalog(chemical_name, chemical_application)

                if "error" in result:
                    return ChemicalLookupResult.error(result["error"])
                elif result.get("hs_code"):
                    return ChemicalLookupResult.success(
                        data=[{
                            "hs_code": result["hs_code"],
                            "cas_number": result.get("cas_number"),
                            "product_family": result.get("product_family", [chemical_name]),
                            "description": f"{chemical_name} - {chemical_application or 'General'}",
                            "from_llm": True
                        }],
                        message="Generated using LLM services"
                    )
                else:
                    return ChemicalLookupResult.error("Could not determine HS code for the chemical")
            else:
                return ChemicalLookupResult.error("No LLM client available for chemical lookup")

        except Exception as e:
            logger.error(f"Error in chemical lookup: {str(e)}")
            return ChemicalLookupResult.error(f"Error in chemical lookup: {str(e)}")

    def _check_historical_data(self, chemical_name: str, chemical_application: str) -> Optional[Dict[str, Any]]:
        """
        Check historical data for the chemical.

        Args:
            chemical_name: Name of the chemical
            chemical_application: Application of the chemical

        Returns:
            Dictionary with chemical data if found, None otherwise
        """
        if self.historical_data is None:
            return None

        try:
            # Look for exact matches first
            exact_matches = self.historical_data[
                self.historical_data['product_name'].str.lower() == chemical_name.lower()
            ]

            if not exact_matches.empty:
                row = exact_matches.iloc[0]
                logger.info(f"Found exact match in historical data for '{chemical_name}': HS Code {row.get('hs_code')}")
                return {
                    "hs_code": row.get('hs_code'),
                    "cas_number": row.get('cas_number'),
                    "product_family": [chemical_name],
                    "description": f"{chemical_name} - {chemical_application or 'General'}",
                    "from_llm": False
                }

            # Look for partial matches
            partial_matches = self.historical_data[
                self.historical_data['product_name'].str.contains(chemical_name, case=False, na=False)
            ]

            if not partial_matches.empty:
                row = partial_matches.iloc[0]
                logger.info(f"Found partial match in historical data for '{chemical_name}': HS Code {row.get('hs_code')}")
                return {
                    "hs_code": row.get('hs_code'),
                    "cas_number": row.get('cas_number'),
                    "product_family": [chemical_name],
                    "description": f"{chemical_name} - {chemical_application or 'General'}",
                    "from_llm": False
                }

            logger.info(f"No match found in historical data for '{chemical_name}', falling back to LLM")

        except Exception as e:
            logger.error(f"Error checking historical data: {e}")

        return None

    def _compute_catalog(self, chemical_name: str, chemical_application: str) -> Dict[str, Any]:
        """
        Compute chemical catalog with historical data check first, then dual LLM verification.

        Args:
            chemical_name: Name of the chemical
            chemical_application: Application of the chemical

        Returns:
            Dictionary with chemical information and HS code
        """
        try:
            # First, check historical data if available
            if self.historical_data is not None:
                historical_result = self._check_historical_data(chemical_name, chemical_application)
                if historical_result:
                    logger.info(f"Found match in historical data for '{chemical_name}'")
                    # Add confidence score for historical data (high confidence)
                    historical_result["confidence_score"] = 0.95
                    historical_result["source_preference"] = "historical_data"
                    return historical_result

            logger.info(f"No historical data match for '{chemical_name}', proceeding with LLM verification")

            # If no historical data found, query both LLM services
            result_cgpt = self.llm_client.get_chemical_info(chemical_name, chemical_application)
            result_p = self.llm_client.query_perplexity(chemical_name, chemical_application)

            # Perform hard match between results
            match_results = self._perform_hard_match(result_cgpt, result_p)

            # Log the raw output for debugging
            raw_output = {
                chemical_name: {
                    "AI Responses": {
                        "ChatGPT": result_cgpt,
                        "Perplexity": result_p
                    },
                    "Match Results": match_results.to_dict()
                }
            }
            logger.debug(f"Raw LLM output: {json.dumps(raw_output, indent=2)}")

            # Return the final result based on match confidence
            return {
                "hs_code": match_results.final_hs_code,
                "cas_number": match_results.final_cas_number,
                "product_family": match_results.final_product_family,
                "confidence_score": match_results.confidence_score,
                "source_preference": match_results.source_preference,
                "from_llm": True
            }

        except Exception as e:
            logger.error(f"Error in catalog computation: {str(e)}")
            return {
                "hs_code": "",
                "error": f"Error in catalog computation: {str(e)}",
                "confidence_score": 0.0,
                "source_preference": "error"
            }

    def _perform_hard_match(self, result_cgpt: Dict[str, Any], result_p: Dict[str, Any]) -> ChemicalMatchResult:
        """
        Perform hard matching between ChatGPT and Perplexity results.

        Args:
            result_cgpt: Result from ChatGPT
            result_p: Result from Perplexity

        Returns:
            ChemicalMatchResult with matching analysis
        """
        match_result = ChemicalMatchResult()

        try:
            # Extract HS codes
            hs_code_cgpt = result_cgpt.get("hs_code", "").strip()
            hs_code_p = result_p.get("hs_code", "").strip()

            # Extract CAS numbers
            cas_cgpt = result_cgpt.get("cas_number", "").strip()
            cas_p = result_p.get("cas_number", "").strip()

            # Extract product families
            family_cgpt = result_cgpt.get("product_family", "").strip()
            family_p = result_p.get("product_family", "").strip()

            # Check HS code match
            if hs_code_cgpt and hs_code_p and hs_code_cgpt != "Error" and hs_code_p != "Error":
                # Check for exact match or partial match (first 6 digits)
                if hs_code_cgpt == hs_code_p:
                    match_result.hs_code_match = True
                    match_result.final_hs_code = hs_code_cgpt
                elif len(hs_code_cgpt) >= 6 and len(hs_code_p) >= 6:
                    if hs_code_cgpt[:6] == hs_code_p[:6]:
                        match_result.hs_code_match = True
                        # Prefer the longer, more specific code
                        match_result.final_hs_code = hs_code_cgpt if len(hs_code_cgpt) >= len(hs_code_p) else hs_code_p

            # Check CAS number match
            if cas_cgpt and cas_p and cas_cgpt != "Error" and cas_p != "Error":
                if cas_cgpt == cas_p:
                    match_result.cas_number_match = True
                    match_result.final_cas_number = cas_cgpt

            # Check product family match (case-insensitive partial match)
            if family_cgpt and family_p and family_cgpt != "Error" and family_p != "Error":
                if family_cgpt.lower() in family_p.lower() or family_p.lower() in family_cgpt.lower():
                    match_result.product_family_match = True
                    # Prefer the more detailed family description
                    match_result.final_product_family = family_cgpt if len(family_cgpt) >= len(family_p) else family_p

            # Calculate confidence score
            matches = sum([
                match_result.hs_code_match,
                match_result.cas_number_match,
                match_result.product_family_match
            ])

            if matches == 3:
                match_result.confidence_score = 1.0
                match_result.source_preference = "consensus"
            elif matches == 2:
                match_result.confidence_score = 0.8
                match_result.source_preference = "consensus"
            elif matches == 1:
                match_result.confidence_score = 0.6
                # Prefer ChatGPT if only one match
                match_result.source_preference = "openai"
            else:
                match_result.confidence_score = 0.3
                # Prefer ChatGPT as default
                match_result.source_preference = "openai"

            # Set final values if not already set by matches
            if not match_result.final_hs_code:
                if match_result.source_preference == "openai":
                    match_result.final_hs_code = hs_code_cgpt if hs_code_cgpt != "Error" else hs_code_p
                else:
                    match_result.final_hs_code = hs_code_p if hs_code_p != "Error" else hs_code_cgpt

            if not match_result.final_cas_number:
                if match_result.source_preference == "openai":
                    match_result.final_cas_number = cas_cgpt if cas_cgpt != "Error" else cas_p
                else:
                    match_result.final_cas_number = cas_p if cas_p != "Error" else cas_cgpt

            if not match_result.final_product_family:
                if match_result.source_preference == "openai":
                    match_result.final_product_family = family_cgpt if family_cgpt != "Error" else family_p
                else:
                    match_result.final_product_family = family_p if family_p != "Error" else family_cgpt

            logger.debug(f"Hard match results: {match_result.to_dict()}")
            return match_result

        except Exception as e:
            logger.error(f"Error in hard matching: {str(e)}")
            # Return default result with ChatGPT preference
            match_result.confidence_score = 0.3
            match_result.source_preference = "openai"
            match_result.final_hs_code = result_cgpt.get("hs_code", "")
            match_result.final_cas_number = result_cgpt.get("cas_number", "")
            match_result.final_product_family = result_cgpt.get("product_family", "")
            return match_result

    def get_hs_codes(self, chemical_name: str, application: str, category: Optional[str] = None) -> Dict[str, Any]:
        """
        Get HS codes for a given chemical and its application (integrated from chemical_controller).

        Args:
            chemical_name: The name of the chemical
            application: The application of the chemical
            category: The category of the chemical (optional)

        Returns:
            Dictionary with status and data fields
        """
        # Validate input
        is_valid, error_message = validate_chemical_input(chemical_name, application)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        # Sanitize inputs
        chemical_name = sanitize_input(chemical_name)
        application = sanitize_input(application)
        if category:
            category = sanitize_input(category)

        # If category is provided but application is not, use category as application
        if not application and category:
            application = category

        # Log the request
        logger.info(f"Looking up HS code for chemical: {chemical_name}, application: {application}")

        try:
            # Call the service to look up the chemical
            result = self.lookup_chemical(chemical_name, application)

            # Format the response
            if result.status == "success" and result.data:
                return {
                    "status": "success",
                    "data": result.data
                }
            else:
                return {
                    "status": "error",
                    "message": result.message or "No HS code found for the given chemical"
                }

        except Exception as e:
            logger.error(f"Error looking up HS code: {str(e)}")
            return {
                "status": "error",
                "message": f"Error looking up HS code: {str(e)}"
            }

# Initialize the integrated chemical service
integrated_chemical_service = IntegratedChemicalService(llm_client)

resolver_post_input = api.model('ResolverInput', {
    'chemical_name': fields.String(required=True, description='Chemical Name'),
    'chemical_application': fields.String(required=False, description='Chemical Application'),
    'hs_code': fields.String(required=False, description='(Optional)If HS Code is already known'),
    'cas_number': fields.String(required=False, description='(Optional)CAS number')
})

resolver_get_input = reqparse.RequestParser()
resolver_get_input.add_argument('chemical_name', type=str, required=True, help='Chemical Name is required')
resolver_get_input.add_argument('chemical_application', type=str, required=False, help='Chemical Application')
resolver_get_input.add_argument('hs_code', type=str, required=False, help='(Optional)If HS Code is already known')
resolver_get_input.add_argument('cas_number', type=str, required=False, help='(Optional)CAS number')


resolver_output = api.model('ResolverOutput', {
    'hs_code': fields.String(description='Resolved HS code'),
    'cas_number': fields.String(description='CAS number of the chemical', required=False),
    'confidence_score': fields.Float(description='Confidence score of the resolution', required=False),
    'source_preference': fields.String(description='Data source used (historical_data/openai/perplexity/consensus)', required=False),
    # 'alternative_hs_codes': fields.List(fields.Nested(api.model('AlternativeCode', {
    #     'code': fields.String(),
    #     'description': fields.String(),
    #     'confidence_score': fields.Float()
    # })))
})

# Enhanced API models for new endpoints
chemical_lookup_input = api.model('ChemicalLookupInput', {
    'chemical_name': fields.String(required=True, description='Chemical Name'),
    'chemical_application': fields.String(required=True, description='Chemical Application'),
    'category': fields.String(required=False, description='Chemical Category (optional)')
})

chemical_lookup_output = api.model('ChemicalLookupOutput', {
    'status': fields.String(description='Status of the lookup'),
    'data': fields.List(fields.Raw, description='Chemical information data'),
    'message': fields.String(description='Additional message', required=False)
})

enhanced_resolver_output = api.model('EnhancedResolverOutput', {
    'hs_code': fields.String(description='Resolved HS code'),
    'cas_number': fields.String(description='CAS number of the chemical', required=False),
    'product_family': fields.String(description='Product family', required=False),
    'confidence_score': fields.Float(description='Confidence score of the resolution', required=False),
    'source_preference': fields.String(description='Preferred source (openai/perplexity/consensus)', required=False),
    'from_llm': fields.Boolean(description='Whether data was generated by LLM', required=False)
})

@ns.route('/resolve')
class CodeResolver(Resource):
    @ns.expect(resolver_post_input, validate=True)
    @ns.marshal_with(resolver_output)
    def post(self):
        """Resolve hs code based on chemical name and application"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /resolve")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Validate input
        chemical_name = data.get('chemical_name', '')
        chemical_application = data.get('chemical_application', '')

        is_valid, error_message = validate_chemical_input(chemical_name, chemical_application)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Validation failed: {error_message}")
            return {"hs_code": "", "error": error_message}, 400

        # Sanitize inputs
        chemical_name = sanitize_input(chemical_name)
        chemical_application = sanitize_input(chemical_application)
        hs_code = sanitize_input(data.get('hs_code', '')) if data.get('hs_code') else None
        cas_number = sanitize_input(data.get('cas_number', '')) if data.get('cas_number') else None

        # Use the integrated chemical service for resolution
        response_data = integrated_chemical_service._compute_catalog(
            chemical_name,
            chemical_application
        )
        logger.info(f"Request [ID: {request_id}] - Successful response with {response_data}")
        return response_data

    @ns.expect(resolver_get_input)
    @ns.marshal_with(resolver_output)
    def get(self):
        """Resolve hs code based on chemical name and application"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /resolve")
        args = resolver_get_input.parse_args()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")

        # Validate input
        chemical_name = args.get('chemical_name', '')
        chemical_application = args.get('chemical_application', '')

        is_valid, error_message = validate_chemical_input(chemical_name, chemical_application)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Validation failed: {error_message}")
            return {"hs_code": "", "error": error_message}, 400

        # Sanitize inputs
        chemical_name = sanitize_input(chemical_name)
        chemical_application = sanitize_input(chemical_application)
        hs_code = sanitize_input(args.get('hs_code', '')) if args.get('hs_code') else None
        cas_number = sanitize_input(args.get('cas_number', '')) if args.get('cas_number') else None

        # Use the integrated chemical service for resolution
        response_data = integrated_chemical_service._compute_catalog(
            chemical_name,
            chemical_application
        )
        logger.info(f"Request [ID: {request_id}] - Successful response with {response_data}")
        return response_data


# Enhanced Chemical Lookup Endpoints with Dual LLM Verification
@ns.route('/lookup')
class ChemicalLookup(Resource):
    @ns.expect(chemical_lookup_input, validate=True)
    @ns.marshal_with(chemical_lookup_output)
    def post(self):
        """Enhanced chemical lookup with dual LLM verification and confidence scoring"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /lookup")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Validate input
        chemical_name = data.get('chemical_name', '')
        chemical_application = data.get('chemical_application', '')

        is_valid, error_message = validate_chemical_input(chemical_name, chemical_application)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": []
            }, 400

        # Sanitize inputs
        chemical_name = sanitize_input(chemical_name)
        chemical_application = sanitize_input(chemical_application)
        category = sanitize_input(data.get('category', '')) if data.get('category') else None

        # If category is provided but application is not, use category as application
        if not chemical_application and category:
            chemical_application = category

        try:
            # Call the integrated chemical service
            result = integrated_chemical_service.lookup_chemical(chemical_name, chemical_application)

            logger.info(f"Request [ID: {request_id}] - Successful response with status: {result.status}")
            return result.to_dict()

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error looking up chemical: {str(e)}",
                "data": []
            }, 500


# GET method for enhanced lookup
chemical_lookup_get_input = reqparse.RequestParser()
chemical_lookup_get_input.add_argument('chemical_name', type=str, required=True, help='Chemical Name is required')
chemical_lookup_get_input.add_argument('chemical_application', type=str, required=True, help='Chemical Application is required')
chemical_lookup_get_input.add_argument('category', type=str, required=False, help='Chemical Category (optional)')

@ns.route('/lookup')
class ChemicalLookupGet(Resource):
    @ns.expect(chemical_lookup_get_input)
    @ns.marshal_with(chemical_lookup_output)
    def get(self):
        """Enhanced chemical lookup with dual LLM verification (GET method)"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /lookup")
        args = chemical_lookup_get_input.parse_args()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")

        # Validate input
        chemical_name = args.get('chemical_name', '')
        chemical_application = args.get('chemical_application', '')

        is_valid, error_message = validate_chemical_input(chemical_name, chemical_application)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Validation failed: {error_message}")
            return {
                "status": "error",
                "message": error_message,
                "data": []
            }, 400

        # Sanitize inputs
        chemical_name = sanitize_input(chemical_name)
        chemical_application = sanitize_input(chemical_application)
        category = sanitize_input(args.get('category', '')) if args.get('category') else None

        # If category is provided but application is not, use category as application
        if not chemical_application and category:
            chemical_application = category

        try:
            # Call the integrated chemical service
            result = integrated_chemical_service.lookup_chemical(chemical_name, chemical_application)

            logger.info(f"Request [ID: {request_id}] - Successful response with status: {result.status}")
            return result.to_dict()

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error looking up chemical: {str(e)}",
                "data": []
            }, 500


# Enhanced Resolver Endpoints (same interface as /resolve but with enhanced functionality)
@ns.route('/resolve-enhanced')
class EnhancedCodeResolver(Resource):
    @ns.expect(resolver_post_input, validate=True)
    @ns.marshal_with(enhanced_resolver_output)
    def post(self):
        """Enhanced HS code resolution with dual LLM verification and confidence scoring"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /resolve-enhanced")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Validate input
        chemical_name = data.get('chemical_name', '')
        chemical_application = data.get('chemical_application', '')

        is_valid, error_message = validate_chemical_input(chemical_name, chemical_application)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Validation failed: {error_message}")
            return {"hs_code": "", "error": error_message}, 400

        # Sanitize inputs
        chemical_name = sanitize_input(chemical_name)
        chemical_application = sanitize_input(chemical_application)
        hs_code = sanitize_input(data.get('hs_code', '')) if data.get('hs_code') else None
        cas_number = sanitize_input(data.get('cas_number', '')) if data.get('cas_number') else None

        try:
            # Use the enhanced resolution method with dual LLM verification
            response_data = integrated_chemical_service._compute_catalog(
                chemical_name,
                chemical_application
            )

            # Ensure from_llm flag is set (it's now determined by the actual data source)
            if "from_llm" not in response_data:
                response_data["from_llm"] = True  # Default to True if not set

            logger.info(f"Request [ID: {request_id}] - Successful response with {response_data}")
            return response_data

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {"hs_code": "", "error": str(e)}, 500

    @ns.expect(resolver_get_input)
    @ns.marshal_with(enhanced_resolver_output)
    def get(self):
        """Enhanced HS code resolution with dual LLM verification (GET method)"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /resolve-enhanced")
        args = resolver_get_input.parse_args()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")

        # Validate input
        chemical_name = args.get('chemical_name', '')
        chemical_application = args.get('chemical_application', '')

        is_valid, error_message = validate_chemical_input(chemical_name, chemical_application)
        if not is_valid:
            logger.warning(f"Request [ID: {request_id}] - Validation failed: {error_message}")
            return {"hs_code": "", "error": error_message}, 400

        # Sanitize inputs
        chemical_name = sanitize_input(chemical_name)
        chemical_application = sanitize_input(chemical_application)
        hs_code = sanitize_input(args.get('hs_code', '')) if args.get('hs_code') else None
        cas_number = sanitize_input(args.get('cas_number', '')) if args.get('cas_number') else None

        try:
            # Use the enhanced resolution method with dual LLM verification
            response_data = integrated_chemical_service._compute_catalog(
                chemical_name,
                chemical_application
            )

            # Ensure from_llm flag is set (it's now determined by the actual data source)
            if "from_llm" not in response_data:
                response_data["from_llm"] = True  # Default to True if not set

            logger.info(f"Request [ID: {request_id}] - Successful response with {response_data}")
            return response_data

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {"hs_code": "", "error": str(e)}, 500


# Chemical Controller Endpoints (integrated from chemical_controller)
@ns.route('/hs-codes')
class ChemicalHSCodes(Resource):
    @ns.expect(chemical_lookup_input, validate=True)
    @ns.marshal_with(chemical_lookup_output)
    def post(self):
        """Get HS codes for a given chemical and its application (from chemical_controller)"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on POST /hs-codes")
        data = request.get_json()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

        # Extract parameters
        chemical_name = data.get('chemical_name', '')
        chemical_application = data.get('chemical_application', '')
        category = data.get('category', None)

        try:
            # Call the integrated chemical service get_hs_codes method
            result = integrated_chemical_service.get_hs_codes(chemical_name, chemical_application, category)

            logger.info(f"Request [ID: {request_id}] - Successful response with status: {result.get('status')}")
            return result

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting HS codes: {str(e)}",
                "data": []
            }, 500

@ns.route('/hs-codes')
class ChemicalHSCodesGet(Resource):
    @ns.expect(chemical_lookup_get_input)
    @ns.marshal_with(chemical_lookup_output)
    def get(self):
        """Get HS codes for a given chemical and its application (GET method)"""
        request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
        logger.info(f"Request received [ID: {request_id}] on GET /hs-codes")
        args = chemical_lookup_get_input.parse_args()
        logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")

        # Extract parameters
        chemical_name = args.get('chemical_name', '')
        chemical_application = args.get('chemical_application', '')
        category = args.get('category', None)

        try:
            # Call the integrated chemical service get_hs_codes method
            result = integrated_chemical_service.get_hs_codes(chemical_name, chemical_application, category)

            logger.info(f"Request [ID: {request_id}] - Successful response with status: {result.get('status')}")
            return result

        except Exception as e:
            logger.error(f"Request [ID: {request_id}] - Error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting HS codes: {str(e)}",
                "data": []
            }, 500

api.add_namespace(ns)



# """Controller for resolving chemical codes and related information."""

# import datetime
# import json
# import logging

# from flask import request
# from flask_restx import Namespace, Resource, fields, reqparse

# from app import api
# from app.services import code_resolver_service_instance

# logger = logging.getLogger(__name__)

# ns = Namespace(name='code-resolver', path="/v1/code-resolver", description='Intelligent Code Resolution APIs')

# resolver_post_input = api.model('ResolverInput', {
#     'chemical_name': fields.String(required=True, description='Chemical Name'),
#     'chemical_application': fields.String(required=False, description='Chemical Application'),
#     'hs_code': fields.String(required=False, description='(Optional)If HS Code is already known'),
#     'cas_number': fields.String(required=False, description='(Optional)CAS number')
# })

# resolver_get_input = reqparse.RequestParser()
# resolver_get_input.add_argument('chemical_name', type=str, required=True, help='Chemical Name is required')
# resolver_get_input.add_argument('chemical_application', type=str, required=False, help='Chemical Application')
# resolver_get_input.add_argument('hs_code', type=str, required=False, help='(Optional)If HS Code is already known')
# resolver_get_input.add_argument('cas_number', type=str, required=False, help='(Optional)CAS number')


# resolver_output = api.model('ResolverOutput', {
#     'hs_code': fields.String(description='Resolved HS code'),
#     'cas_number': fields.String(description='CAS number of the chemical', required=False),
#     # 'confidence_number': fields.Float(description='Confidence score of the resolution'),
#     # 'alternative_hs_codes': fields.List(fields.Nested(api.model('AlternativeCode', {
#     #     'code': fields.String(),
#     #     'description': fields.String(),
#     #     'confidence_score': fields.Float()
#     # })))
# })

# @ns.route('/resolve')
# class CodeResolver(Resource):
#     @ns.expect(resolver_post_input, validate=True)
#     @ns.marshal_with(resolver_output)
#     def post(self):
#         """Resolve hs code based on chemical name and application"""
#         request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
#         logger.info(f"Request received [ID: {request_id}] on POST /resolve")# from IP: {client_ip}")
#         data = request.get_json()
#         logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(data)}")

#         response_data = code_resolver_service_instance.resolve_code(
#             data['chemical_name'],
#             data['chemical_application'],
#             data.get('hs_code'),
#             data.get('cas_number')
#         )
#         logger.info(f"Request [ID: {request_id}] - Successful response with {response_data}")
#         return response_data

#     @ns.expect(resolver_get_input)
#     @ns.marshal_with(resolver_output)
#     def get(self):
#         """Resolve hs code based on chemical name and application"""
#         request_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + "-" + str(hash(str(request.headers)))[-6:]
#         logger.info(f"Request received [ID: {request_id}] on GET /resolve")# from IP: {client_ip}")
#         args = resolver_get_input.parse_args()
#         logger.info(f"Request [ID: {request_id}] - Query: {json.dumps(args)}")
#         response_data = code_resolver_service_instance.resolve_code(
#             args['chemical_name'],
#             args['chemical_application'],
#             args.get('hs_code'),
#             args.get('cas_number')
#         )
#         logger.info(f"Request [ID: {request_id}] - Successful response with {response_data}")
#         return response_data

# api.add_namespace(ns)
