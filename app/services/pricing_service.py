import requests
import os
import pandas as pd
from dotenv import load_dotenv
import concurrent.futures
from langchain_openai import ChatOpenAI
from sqlalchemy import create_engine
from langchain_community.utilities.sql_database import SQLDatabase
from langchain_community.agent_toolkits import create_sql_agent
from langchain.schema import HumanMessage, SystemMessage
from openai import OpenAI
from app.services.perplexity_client import *
from .agents.sql_agent import *
from app.services.agents.duty_rate_agent import *
from .agents.summarizer_agent import *

# from utils import *
from ..utils.llm_utilites import *
from .agents import duty_rate_agent_obj
from .agents import sql_agent_obj
from .agents import summarizer_agent_obj
from app.utils.llm_utilites import LLMUtils
class PricingService:
    def __init__(self):
        pass

    def tariff_iq(self, user_query):
        """Orchestrates the flow of execution."""
        # Step 1: Extract Parameters for Import Duties
        #duty_params = llm_extract_duty_params(user_query)
        #duty_params = json.loads(duty_params) #-> not needed with perplexity
        duty_params = LLMUtils.llm_extract_duty_params_pereplexity(user_query)
        print(duty_params)
        if (duty_params['product_name']==None) or (duty_params['product_name']=="None") or (duty_params['product_name']=="none"):
            return "I am AI system meant for only answering tariff related questions for US. Looks like your query does not mention any chemical product. Please specify product name and import country of interest. Thanks"
        
        print(duty_params['country_code'])
        if duty_params['country_code']=="N/A":
            duty_params['country_code'] = None
            print(duty_params['country_code'])
        #exit(1)

        #Step 2: Compute Duty basis the user query 
        #old code - semantic_hs_code = duty_response['standard duty']['hs_code']
        duty_response = duty_rate_agent_obj.calculate_duty_rate(duty_params['product_name'], duty_params['country_code'],duty_params['hts_number'])
        if (duty_response =="Sorry, I am not able to retrieve the tariff for the specified product. Try providing an accurate product name or HS Code. Thanks."):
            return "Sorry, I am not able to retrieve the tariff for the specified product. Try providing an accurate product name or HS Code. Thanks."
        
        print("******************************")
        print("******************************")
        print(duty_response) #now a dict
        print("******************************")
        print("******************************")


        if (duty_params['country_code']==None):
            for key in duty_response['standard duty'].keys():
                # print(key)
                # #print(key['hts_code'])
                # print(duty_response['standard duty'][key])
                semantic_hs_code = duty_response['standard duty'][key]['hs_code']
                break
        else:
            semantic_hs_code = duty_response['standard duty']['hs_code']
            
        print(semantic_hs_code)
        #exit(1)

        # Step 3: Fetch Market Intelligence
        market_intelligence_prompt =f"User Query:{user_query}\n"
        market_intelligence_prompt += f"Additional Context on Duty from our iternal tariff database: {duty_response}\n"
        market_intelligence_prompt += f"For the above User Query, and given the Additional Context, extract relevant market intellgence to help answer the user query."
        market_intelligence_prompt += f"Do add any announcements on upcoming or anticipated changes in duties where relevant."
        market_intelligence_prompt += f"Please dont provide any proprietary information - specific supplier names or other details in your response. Keep it at an aggregate level"
        #market_intelligence_prompt += f"
        #print(market_intelligence_prompt)

        # Step 4: Augment Query for Procurement Mix
        #augmented_query = llm_generate_augmented_query(market_intelligence)
        augmented_query = user_query + f"Additional Context for User Query: {str(duty_params)}"
        augmented_query += "You are a procurement mix specialist; Refactor the above user query in the context of efficient procurement"
        augmented_query += "Your job is to only talk about alternate countries of origin to help balance US firm's procurement portfolio."
        augmented_query += "What are other countries the US firm can import the HS Code and how much total quanity (Total_STD_QTY)  are available from these other altrenaitves." #how many shipper
        #print(augmented_query)
        #exit(1)
        
        # Step 4: Run Sub-Routines 2 and 3 in parallel
        with concurrent.futures.ThreadPoolExecutor() as executor:
            use_llm_sql_agent = 0 # 0 - python utility 1 - llm-agent 
            procurement_future = executor.submit(sql_agent_obj.execute, augmented_query,semantic_hs_code, duty_params['country_code'],use_llm_sql_agent)
            #MI_future = executor.submit(perplexity_agent, market_intelligence_prompt)

            procurement_mix = procurement_future.result()
            print(procurement_mix)

            #market_intelligence_response = MI_future.result()
            #print(market_intelligence_response)
            #market_intelligence_response_choices = market_intelligence_response.get("choices", [])
            #market_intelligence = market_intelligence_response_choices[0].get("message", {}).get("content", "")
            #print(market_intelligence)

            #exit(1)
        
        # Step 5: Summarize the results
        final_summary = summarizer_agent_obj.summarize_results(procurement_mix, duty_response, user_query, duty_params['country_code'])
        return final_summary
    
    def _market_intelligence_payload(self, query:str):
        return {
            "model": "sonar-pro",
            "messages": [
                {
                    "role": "system",
                    "content": "Be precise and concise."
                },
                {
                    "role": "user",
                    "content": query
                }
            ],
            "max_tokens": 512,            # Optional limit on response length
            "temperature": 0.2,
            "top_p": 0.9,
            # Adjust the domain filter if you want to restrict or broaden the search
            "search_domain_filter": [],          # Example: ["perplexity.ai"] if you only want results from perplexity.ai
            "return_images": False,
            "return_related_questions": True,
            "search_recency_filter": "day",
            "top_k": 0,
            "stream": False,
            "presence_penalty": 0,
            "frequency_penalty": 1,
            "response_format": None
        }