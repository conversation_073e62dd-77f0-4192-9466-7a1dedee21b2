import logging
import os
import pandas as pd

logger = logging.getLogger(__name__)

class FileLoaderService:
    def __init__(self):
        # self.semantic_lookup = HTSSemanticLookup()
        # self.llm = openai_o3_client
        # self.historical_df = get_historical_data()
        pass


    def load_csv_file(self, file_path: str):
        """
        load a file from a location to memory
        """
        logger.debug(f"loading file from file_path: {file_path}")

        if not os.path.isabs(file_path):
            file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), file_path)
        
        try:
            if not os.path.exists(file_path):
                logger.error(f"File not found at {file_path}")
                return pd.DataFrame()
                
            # current_app.logger.info(f"Loading historical data from {file_path}")
            return pd.read_csv(file_path)
            
        except Exception as e:
            return pd.DataFrame()
