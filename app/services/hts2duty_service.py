import sys
import os
import logging
from typing import Dict, Any, Optional

# Add the hts2duty directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'hts2duty'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'hts2duty', 'src'))

from src.tariff_service import TariffService
from src.tariff.extract_hts import HTSIndices, HTSSemanticLookup
from src.agents.tariff_extractor_agent import extract_tariff_info_structured_assist_openAI

logger = logging.getLogger(__name__)


class HTS2DutyService:
    """
    Service wrapper for HTS2Duty functionality to replace external tariff API calls.
    Provides the same interface as external APIs but uses local HTS2Duty data.
    """
    
    def __init__(self):
        """Initialize the HTS2Duty service with required components."""
        self.tariff_service = None
        self.lookup = None
        self._initialize_services()
    
    def _initialize_services(self):
        """Initialize the tariff service and HTS lookup system."""
        try:
            logger.info("Initializing HTS2Duty services...")
            
            # Initialize TariffService
            self.tariff_service = TariffService()
            
            # Initialize HTS indices and lookup
            hts_indices_dir = os.path.join(
                os.path.dirname(__file__), '..', '..', 'hts2duty', 'hts_indices'
            )
            hts_data_file = os.path.join(
                os.path.dirname(__file__), '..', '..', 'hts2duty', 'htsdata_all.json'
            )
            
            # Load indices with auto-creation if needed
            indices = HTSIndices.load(hts_indices_dir, auto_create=True, data_file=hts_data_file)
            self.lookup = HTSSemanticLookup(indices)
            
            logger.info("HTS2Duty services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize HTS2Duty services: {str(e)}")
            raise
    
    def standardize_code_hts(self, code: str) -> str:
        """
        Standardize HTS code format.
        
        Args:
            code: Raw HTS code
            
        Returns:
            Standardized HTS code in format XXXX.XX.XX.XX
        """
        # Remove all non-numeric characters
        numbers = ''.join(char for char in code if char.isdigit())
        
        # Pad with zeros if less than 10 digits
        if len(numbers) <= 12:
            numbers = numbers.ljust(10, '0')
        elif len(numbers) > 12:
            raise ValueError("Input contains more than 10 digits")
        
        # Format into the desired pattern
        return f"{numbers[0:4]}.{numbers[4:6]}.{numbers[6:8]}.{numbers[8:10]}"
    
    def get_tariff_data(self, hs_code: str, country: str) -> Dict[str, Any]:
        """
        Get tariff data for a given HS code and country.
        
        Args:
            hs_code: The HS code
            country: The country name
            
        Returns:
            Dictionary with tariff data in the same format as external API
        """
        try:
            logger.debug(f"Getting tariff data for HS code: {hs_code}, Country: {country}")
            
            if not self.lookup or not self.tariff_service:
                raise Exception("HTS2Duty services not properly initialized")
            
            # Standardize the HTS code
            standardized_hts_code = self.standardize_code_hts(hs_code)
            logger.debug(f"Standardized HTS code: {standardized_hts_code}")
            
            # Lookup HTS data
            tariff_results = self.lookup.lookup_hts(standardized_hts_code)
            logger.debug(f"Tariff lookup results: {tariff_results}")
            
            if not tariff_results:
                return {
                    "error": f"No tariff data found for HS code {hs_code}",
                    "hs_code": hs_code,
                    "country": country
                }
            
            tariff_results_list = [tariff_results]
            
            # Get country-specific tariff information
            Standard_duty, Incremental_duty, Proposed_duty, ad_value, cd_value = \
                self.tariff_service.get_country_tariff(tariff_results_list, country)
            
            # Create response blob
            json_blob = {
                'Country': country,
                'Standard Tariff': Standard_duty,
                'New Tariff': Incremental_duty,
                'Proposed Tariff': Proposed_duty,
                'Anti-Dumping Duty': ad_value,
                'Countervailing Duty': cd_value
            }
            
            # Extract structured tariff information
            tariff_response = extract_tariff_info_structured_assist_openAI(json_blob)

            # Parse the response if it's a string (which it usually is from OpenAI)
            if isinstance(tariff_response, str):
                try:
                    import json
                    # Try to parse as JSON
                    tariff_response = json.loads(tariff_response)
                except json.JSONDecodeError:
                    # If it's not valid JSON, create a structured response
                    logger.warning(f"Could not parse tariff response as JSON: {tariff_response}")
                    tariff_response = {
                        "Country": country,
                        "Standard Tariff": Standard_duty,
                        "New Tariff": Incremental_duty,
                        "Proposed Tariff": Proposed_duty,
                        "Anti-Dumping Duty": str(ad_value) + "%",
                        "Countervailing Duty": str(cd_value) + "%",
                        "Total Duty": "N/A",
                        "raw_response": tariff_response
                    }

            logger.debug(f"Final tariff response: {tariff_response}")
            return tariff_response
            
        except Exception as e:
            logger.error(f"Error getting tariff data for {country}, {hs_code}: {str(e)}")
            return {
                "error": f"Failed to get tariff data: {str(e)}",
                "hs_code": hs_code,
                "country": country
            }
    
    def fetch_tariff_data(self, country: str, hs_code: str) -> Dict[str, Any]:
        """
        Fetch tariff data - compatibility method with existing API interface.
        
        Args:
            country: Country name
            hs_code: HS code
            
        Returns:
            Dictionary with tariff data
        """
        return self.get_tariff_data(hs_code, country)


# Global instance for reuse
_hts2duty_service_instance = None


def get_hts2duty_service() -> HTS2DutyService:
    """
    Get a singleton instance of HTS2DutyService.
    
    Returns:
        HTS2DutyService instance
    """
    global _hts2duty_service_instance
    if _hts2duty_service_instance is None:
        _hts2duty_service_instance = HTS2DutyService()
    return _hts2duty_service_instance
