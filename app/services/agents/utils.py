import json
import re

def extract_fields(response: dict):
    # Extract the content from choices
    choices = response.get("choices", [])
    if not choices:
        return None
    
    content = choices[0].get("message", {}).get("content", "")
    
    # Extract JSON-like structure within markdown
    match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)
    if match:
        try:
            json_data = json.loads(match.group(1))
            return {
                "product_name":json_data.get("product_name"),
                "hts_number": json_data.get("hts_number"),
                #"hs_code": json_data.get("hs_code"),
                "country_code": json_data.get("country_code")
            }
        except json.JSONDecodeError:
            pass
    
    return None
