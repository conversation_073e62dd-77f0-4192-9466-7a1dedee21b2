from openai import OpenAI
from app.services import openai_gpt4_client
from app.services.openai_client import OpenAIClient
from app.utils.hts_duty_calculator import *
from app.utils.extract_hts import *
import pandas as pd
import sys
import os
from dotenv import load_dotenv
from app.config import OPENAI_API_KEY
load_dotenv()
from app.services.tariff_service import TariffService

class DutyRateAgent:

    def __init__(self):
        print("Invoking Duty Calculator")
        # Load file from resources in root folder
        RESOURCES_DIR = os.path.abspath(os.path.join(
            os.path.dirname(__file__), "../../../resources"))

        if not os.path.exists(RESOURCES_DIR):
            print("Building indices for the first time...")
            RESOURCE_FILE = os.path.join(RESOURCES_DIR, 'htsdata_all.json')
            # Load raw data
            with open(RESOURCE_FILE, 'r') as file:
                hts_data = json.load(file)

            # Build indices
            builder = HTSIndexBuilder()
            indices = builder.build_indices(hts_data)

            # Save indices
            indices.save(RESOURCES_DIR)
            print("Indices built and saved successfully.")
        else:
            print("Loading pre-computed indices...")
            indices = HTSIndices.load(RESOURCES_DIR)

        # Create lookup system with loaded indices
        self.duty_rate_lookup = HTSSemanticLookup(indices)


    def calculate_duty_rate(self, product_name, country_code, hts_number=None):
        print("Invoking Duty Calculator")
        # Path for storing indices
        
        semantic_results = self.duty_rate_lookup.semantic_search(product_name)
        print(semantic_results[0])
        #exit(1)
        semantic_similarity_score = semantic_results[0]['similarity_score']
        if len(semantic_results) == 0:
            #use hts code from perplexity
            hts_code = self.standardize_code(hts_number)
            print(hts_code)
            perplexity_results = self.duty_rate_lookup.lookup_hts(hts_code)
            if perplexity_results==None:
                return "Sorry, I am not able to retrieve the tariff names. Try providing an accurate product name or HS Code. Thanks "

            #print(perplexity_results)
            perplexity_results_list=[perplexity_results]
            if country_code == None:
                duty = HTSDutyUtils.parse_hts_data_all_countries(perplexity_results_list)    
            else:
                duty = HTSDutyUtils.parse_hts_data(perplexity_results_list,country_code)     
        else:
            # print(semantic_results[0])
            semantic_similarity_score = semantic_results[0]['similarity_score']
            print(semantic_similarity_score)
            #exit(1)
            if (semantic_similarity_score>=0.85):
                top_k_results = 1
                if country_code == None:
                    duty = HTSDutyUtils.parse_hts_data_all_countries(semantic_results[:top_k_results])
                else:
                    duty = HTSDutyUtils.parse_hts_data(semantic_results[:top_k_results],country_code)                 
            else:
                #use hts code from perplexity
                hts_code = self.standardize_code(hts_number)
                print(hts_code)
                perplexity_results = self.duty_rate_lookup.lookup_hts(hts_code)
                print(perplexity_results)
                if perplexity_results==None:
                    return "Sorry, I am not able to retrieve the tariff for the specified product. Try providing an accurate product name or HS Code. Thanks."
                #print(perplexity_results)
                perplexity_results_list=[perplexity_results]
                if country_code == None:
                    duty = HTSDutyUtils.parse_hts_data_all_countries(perplexity_results_list)  
                else:
                    duty = HTSDutyUtils.parse_hts_data(perplexity_results_list,country_code) 

        Incremental_duty = "NA"
        Proposed_duty = "NA"  
        tf_service = TariffService()  
        hts_code = self.standardize_code(hts_number)                
        print(hts_code)

        Standard_duty, Incremental_duty, Proposed_duty,ad_value, cd_value = tf_service.get_country_tariff(hts_code, duty, country_code)  
        return {"standard duty": Standard_duty, "recently announced incremental duty enforced by Trump": Incremental_duty, "Proposed announcement of reciprocal duty effective July 4th 2025":Proposed_duty, "Anti-Dumping Duty":ad_value, "Counter Vailing Duty":cd_value }  

    def extract_duty_params(self, query):
        """Use LLM to extract HTS number and country code for import duty calculation."""
        json_schema = {
            "product_name": "Methyl methacrylate",
            "hts_number": "2916.14.00.00",
            "country_code": "KR"
        }
        prompt = f"""Extract the specified product name, HTS number (10 digit full code) and 2-alphabet country code from this query: {query}.
            If not specified, suggest relevant HTS and 2-alphabet country code for tariff duty calculation.
        """
        client = OpenAI()
        response = client.chat.completions.create(
            model="gpt-4o",  
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": "Provide valid JSON output. The data schema should be like this:" +
                 json.dumps(json_schema)},
                {"role": "user",
                 "content": [
                     {
                         "type": "text",
                         "text": f"{prompt}"
                     },
                 ],
                 }
            ],
            # max_tokens=self.max_tokens, #wih gpt-4 better to not restrict else it leaves the chat incomplete.#also max-toekns include prompt tokens as well
        )

        response_content = response.choices[0].message.content
        try:
            return response_content
        except json.JSONDecodeError:
            # Fallback in case of errors
            return {"product_name": None, "hts_number": None, "country_code": None}
        

    def standardize_code(self, code: str) -> str:
        # Remove all non-numeric characters
        numbers = ''.join(char for char in code if char.isdigit())
        
        # Pad with zeros if less than 10 digits
        if len(numbers) < 10:
            numbers = numbers.ljust(10, '0')
        elif len(numbers) > 10:
            raise ValueError("Input contains more than 10 digits")
        
        # Format into the desired pattern
        return f"{numbers[0:4]}.{numbers[4:6]}.{numbers[6:8]}.{numbers[8:10]}"