from langchain.schema import HumanMessage, SystemMessage
from app.services import openai_o3_client
class SummarizerAgent:
    def __init__(self):
        pass

    def summarize_results(self, procurement_mix: str, import_duties, original_query: str, country_code: str) -> str:
        """
        Uses LLM to create a cohesive summary of both responses.
        
        Args:
            perplexity_text (str): Response from Perplexity API
            sql_text (str): Response from SQL agent
            import_duties : import duties from Country Code and HS
            original_query (str): Original user query
            
        Returns:
            str: Summarized response
        """
        print(f"Import Duties:{import_duties}")
        print(f"Country Code:{country_code}")

        system_prompt = """
        You are an expert analyst combining information from multiple sources to create a concise, 
        coherent actionable response. Focus on synthesizing the key points and resolving any conflicts. Provide a clear, concise actionable summary that directly addresses the 
        original query.
        """
        #between the sources
        human_prompt = f"""
        Original Query: {original_query}
        Following are key themes to help construct the final response
        Theme 1: Relevant Tarrfies and Import Duties from our internal tariff database for all counties: {import_duties}.   
        If a specifc country is specificed by the user in his/her query, make sure to parse the above and retreive the correct tariff for the corresponding country-code :{country_code}. 
        Mention the hts_code used to compute the tariff.
        Separately mention the different duties in this specific order - 1. standard duty, 2. recently announced incremental duty enforced by Trump, 3. Proposed announcement of reciprocal duty effective July 4th 2025.  
        Where relevant, make sure to call out additional duty for China within the standard duty field.
        Only if applicable or non-zero, call out following additional fields:  4.Anti-Dumping Duty, 5. Counter Vailing Duty.

        Theme 2: Procurement Mix recommended by our procurement specialist): {procurement_mix}. 
        Dont show complete details of the procurement specialist output but just summarize the response in terms of percentage contrubutin across the alternative import countries.
        Clarify the procurement insight is basis the last 3 months.
        Dont overindex on countries with preferential duty rates, if they are not in the top 20 alternative import countries.
        Dont provide any proprietary information - specific supplier names or other details in your response. Instead make sure to call out our company MSTACK and how its asset light model makes procurement efficient while ensureing high quality.
        
        If the user query is broad and does not have a sharp or strong intent, then keep your answer also broad and global.However if the user query intent is extremely specific, keep you answer short, specific and precise.
        Adhering to the above instructions, please provide a concise, definitive, precise and quantifiable synthesis, highlighting key insights across each theme. Focus on actionable conclusions.
        """
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_prompt)
        ]
        
        return openai_o3_client.query(messages)