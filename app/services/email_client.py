import boto3
from botocore.exceptions import Client<PERSON>rror
import os
from app.config import AWS_REGION, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, EMAIL_SENDER

class EmailClient:
    def __init__(self):
        self.ses_client = boto3.client(
            'ses',
            region_name=AWS_REGION,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY
        )

    def send_email(self, recipient, subject, body_text, body_html=None):
        """
        Send an email using Amazon SES.

        :param sender: Email address of the sender.
        :param recipient: Email address of the recipient.
        :param subject: Subject of the email.
        :param body_text: Plain text body of the email.
        :param body_html: HTML body of the email.
        :return: None
        """
        try:
            response = self.ses_client.send_email(
                Destination={
                    'ToAddresses': [recipient],
                },
                Message={
                    'Body': {
                        # 'Html': {
                        #     'Charset': "UTF-8",
                        #     'Data': body_html,
                        # },
                        'Text': {
                            'Charset': "UTF-8",
                            'Data': body_text,
                        },
                    },
                    'Subject': {
                        'Charset': "UTF-8",
                        'Data': subject,
                    },
                },
                Source=EMAIL_SENDER,
            )
        except ClientError as e:
            print(f"Error sending email: {e.response['Error']['Message']}")
        else:
            print(f"Email sent! Message ID: {response['MessageId']}")