from typing import List
import json
import os
import yaml
from dotenv import load_dotenv
import pandas as pd
from datetime import datetime

class TariffService:
    def __init__(self):
        self.config = self._load_config()
        self.proposed_tariffs = self._load_proposed_tariffs()
        self.exempt_hs_codes = self._load_exempt_hs_codes()
        self.ad_cd_data = self._load_ad_cd_data()

    def _load_config(self):
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'tariff_config.yaml')
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)

    def _load_proposed_tariffs(self):
        csv_path = os.path.join(os.path.dirname(__file__), self.config['proposed_tariffs']['csv_path'])
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)
            return dict(zip(df['country_code'], df['tariff_percentage']))
        return {}

    def _load_exempt_hs_codes(self):
        exempt_path = os.path.join(os.path.dirname(__file__), 
                                 self.config['proposed_tariffs']['exempt_codes_path'])
        if os.path.exists(exempt_path):
            with open(exempt_path, 'r') as f:
                return {line.strip() for line in f if line.strip()}
        return set()

    def _load_ad_cd_data(self):
        csv_path = os.path.join(os.path.dirname(__file__), self.config['proposed_tariffs']['ad_cd_path'])
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)
            # Create a dictionary with (hs_code, country) as key and (AD, CD) as value
            return {(str(row['hs_code']), row['country']): (row['AD'], row['CD']) 
                   for _, row in df.iterrows()}
        return {}

    def _get_ad_cd_values(self, hs_code, country):
        #first 8 digit
        cleaned_hs = ''.join(c for c in hs_code if c.isdigit())[:8]
        print(cleaned_hs,country)
        # Try to get AD/CD values, default to 0 if not found
        ad, cd = self.ad_cd_data.get((cleaned_hs, country), (-1, -1))
        if ad == -1 and cd ==-1:
            #first 6 digits
            # Clean HS code to match format in CSV
            cleaned_hs = ''.join(c for c in hs_code if c.isdigit())[:6]
            print(cleaned_hs,country)
            # Try to get AD/CD values, default to 0 if not found
            ad, cd = self.ad_cd_data.get((cleaned_hs, country), (0, 0))

        print(ad, cd)
        return ad, cd

    def _is_hs_code_exempt(self, hs_code):
        # Remove dots and spaces from HS code for comparison
        cleaned_hs = ''.join(c for c in hs_code if c.isdigit())
        # Take only first 8 digits for comparison
        cleaned_hs = cleaned_hs[:8]
        #print(cleaned_hs)
        # Similarly clean and truncate the exempt codes for comparison
        exempt_codes_8digit = {code[:8] for code in self.exempt_hs_codes}
        return cleaned_hs in exempt_codes_8digit

    def get_country_tariff(self, hs_code, Standard_duty, country_code):

        Incremental_duty = "NA"
        Proposed_duty = "NA"
        ad_value = "NA"    
        cd_value = "NA"    
        print(country_code)
        if country_code != None:
            print(hs_code, country_code)
            ad_value, cd_value = self._get_ad_cd_values(hs_code, country_code) if hs_code else (0, 0)
            #print(ad_value, cd_value)    
            #exit(1)        
            # Get incremental duty from config if it exists
            if country_code in self.config['incremental_duties']:
                Incremental_duty = self.config['incremental_duties'][country_code]['description']
                print(Incremental_duty)
        
            # Get proposed duty if it exists and HS code is not exempt
            if hs_code and country_code in self.proposed_tariffs:
                if not self._is_hs_code_exempt(hs_code):
                    effective_date = self.config['proposed_tariffs']['effective_date']
                    tariff_value = self.proposed_tariffs[country_code]
                    Proposed_duty = f"Additional {tariff_value}% duty effective from {effective_date}"
                    print(Proposed_duty)
                else:
                    print(f"HS code {hs_code} is exempt from proposed tariffs")
        else:
            Incremental_duty = """Trump's recent announcement enforces:\n1. 25% additional duty on imports from Canada, with a reduced 10% tax on Canadian energy products such as oil and electricity.\n2. 25% additional duty on imports from Mexico"\n3. 20% additional duty on imports from China"\n"""
            Proposed_duty = "Trump has proposed additianl recipriocal duty for specific countries starting July 4th 2025"
            ad_value = "There could be country/supplier specific anti-dumpting duty in place"    
            cd_value = "There could be country/supplier specific counter-vailing duty in place"                
            
        return Standard_duty, Incremental_duty, Proposed_duty, ad_value, cd_value