import pandas as pd
from sqlalchemy import create_engine, text
from datetime import datetime
import logging

import os
from dotenv import load_dotenv
load_dotenv()  # take environment variables from .env.

DB_URL = os.getenv("DB_URL")
DB_USERNAME = os.getenv("DB_USERNAME")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_db_connection(db_params):
    """Create database connection using SQLAlchemy"""
    try:
        connection_string = f"postgresql://{db_params['user']}:{db_params['password']}@{db_params['host']}:{db_params['port']}/{db_params['database']}"
        engine = create_engine(connection_string)
        return engine
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        raise

import pandas as pd
from typing import Dict, Any
from collections import Counter


def read_and_process_data(engine):
    """Read data from source table and process it"""
    try:
        # Read data from source table
        query = """SELECT date,quantity,hs_code,std_qty,origin_port,unit_rate_usd,fob_value_usd,is_import,country_name,hs_code_description,global_unit_id,global_std_unit_id,parent_global_exporter_id,universal_exporter_id,parent_global_importer_id,universal_importer_id,parent_coo,parent_cod,parent_poo,parent_pod,universal_poo,universal_pod,universal_unit_id,universal_std_unit_id,shipper_city,shipper_pin,shipper_state,shipper_country,consignee_city,consignee_pin,consignee_state,consignee_country,coo_continent_id,cod_continent_id,product_desc
        FROM volza_trade_data"""
        df = pd.read_sql(query, engine)
        df['date'] = pd.to_datetime(df['date'], format='%d-%b-%Y')
        # print(df.head())
        # df.to_csv("sample_volza_trade_data.csv")
        # exit(1)
        
        # Create 'mm-yy' column
        df['mm-yy'] = df['date'].dt.strftime('%m-%Y')       
        # Group by required columns and create summary
        summary_df = df.groupby(['mm-yy','hs_code','hs_code_description','product_desc','parent_global_importer_id', 'parent_global_exporter_id','parent_coo','parent_cod']).agg(
            total_quantity=('quantity', 'sum'),
            total_std_qty=('std_qty', 'sum'),
            total_fob_value=('fob_value_usd', 'sum'),
            avg_unit_rate=('unit_rate_usd', 'mean'),
            transaction_count=('date', 'count')  # Number of transactions
        ).reset_index()
        summary_df.columns = ['MONTH-YEAR','HS_Code','HS_CODE_DESCRIPTOR','PRODUCT_DETAILS','IMPORTER_ID','EXPORTER_ID','ORIGIN_COUNTRY','DESTINATION_COUNTRY','Total_QTY','Total_STD_QTY','TOTAL_FOB_Value','AVG_UNIT_RATE','TRANSACTION_COUNT']
        #summary_df.to_csv("sample_volza_trade_data_summary.csv")
        print(summary_df.shape)
        summary_df.to_parquet("sample_volza_trade_data_summary.parquet", index=False)
        return summary_df
    
    except Exception as e:
        logger.error(f"Data processing error: {str(e)}")
        raise

def main():
    # Database connection parameters
    db_params = {
        'host': DB_URL,
        'port': '5432',
        'database': DB_NAME,
        'user': DB_USERNAME,
        'password': DB_PASSWORD
    }
    
    try:
        # Create database connection
        engine = create_db_connection(db_params)
        logger.info("Database connection established")
        
        # Process data
        summary_df = read_and_process_data(engine)
        logger.info(f"Processed {len(summary_df)} summary records")
        #exit(1)
        
    except Exception as e:
        logger.error(f"ETL process failed: {str(e)}")
        raise
    finally:
        if 'engine' in locals():
            engine.dispose()

if __name__ == "__main__":
    main()