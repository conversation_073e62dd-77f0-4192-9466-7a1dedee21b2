from dataclasses import dataclass
from typing import List, Optional, Dict, Any


@dataclass
class Supplier:
    """
    Represents a supplier with trade data and contact information.
    """
    name: str
    country: str
    transaction_count: int
    export_volume_tons: float
    avg_price_per_ton: float
    total_export_value: float
    unit: str = "KGS"
    global_exporter_id: Optional[str] = None
    average_quantity_per_shipment: Optional[float] = None
    minimum_fob: Optional[float] = None
    maximum_fob: Optional[float] = None
    from_llm: bool = False
    
    # Contact information
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    website: Optional[str] = None
    
    # Business information
    certifications: Optional[List[str]] = None
    advantages: Optional[List[str]] = None
    overview: Optional[str] = None
    
    # Ranking information
    rank: Optional[int] = None
    score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "name": self.name,
            "country": self.country,
            "transaction_count": self.transaction_count,
            "export_volume_tons": self.export_volume_tons,
            "avg_price_per_ton": self.avg_price_per_ton,
            "total_export_value": self.total_export_value,
            "unit": self.unit,
            "global_exporter_id": self.global_exporter_id,
            "average_quantity_per_shipment": self.average_quantity_per_shipment,
            "minimum_fob": self.minimum_fob,
            "maximum_fob": self.maximum_fob,
            "from_llm": self.from_llm,
            "email": self.email,
            "phone": self.phone,
            "address": self.address,
            "website": self.website,
            "certifications": self.certifications,
            "advantages": self.advantages,
            "overview": self.overview,
            "rank": self.rank,
            "score": self.score
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Supplier':
        """Create Supplier instance from dictionary."""
        # Handle different possible field names from API responses
        total_value = data.get("total_export_value", 0)
        if not total_value:
            # Calculate from avg_price_per_ton and export_volume_tons if available
            avg_price = data.get("avg_price_per_ton", 0) or data.get("average_fob", 0)
            volume = data.get("export_volume_tons", 0) or data.get("total_quantity", 0)
            if avg_price and volume:
                total_value = avg_price * volume
        
        # Handle contact information if present
        contact_info = data.get("contact", {})
        
        return cls(
            name=data.get("name", "") or data.get("exporter_name", "Unknown"),
            country=data.get("country", "Unknown"),
            transaction_count=data.get("transaction_count", 0) or data.get("shipment_count", 0),
            export_volume_tons=data.get("export_volume_tons", 0) or data.get("total_quantity", 0),
            avg_price_per_ton=data.get("avg_price_per_ton", 0) or data.get("average_fob", 0),
            total_export_value=total_value,
            unit=data.get("unit", "KGS"),
            global_exporter_id=data.get("global_exporter_id"),
            average_quantity_per_shipment=data.get("average_quantity_per_shipment"),
            minimum_fob=data.get("minimum_fob"),
            maximum_fob=data.get("maximum_fob"),
            from_llm=data.get("from_llm", False),
            email=contact_info.get("email") if contact_info else data.get("email"),
            phone=contact_info.get("phone") if contact_info else data.get("phone"),
            address=contact_info.get("address") if contact_info else data.get("address"),
            website=contact_info.get("website") if contact_info else data.get("website"),
            certifications=data.get("certifications"),
            advantages=data.get("advantages"),
            overview=data.get("overview"),
            rank=data.get("Rank"),
            score=data.get("Score")
        )


@dataclass
class SupplierLookupResult:
    """
    Represents the result of a supplier lookup operation.
    """
    status: str  # "success", "error", "warning"
    data: Optional[List[Dict[str, Any]]] = None
    message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        result = {
            "status": self.status
        }
        
        if self.data is not None:
            result["data"] = self.data
            
        if self.message is not None:
            result["message"] = self.message
            
        return result
    
    @classmethod
    def success(cls, data: List[Dict[str, Any]], message: Optional[str] = None) -> 'SupplierLookupResult':
        """Create a successful result."""
        return cls(status="success", data=data, message=message)
    
    @classmethod
    def error(cls, message: str) -> 'SupplierLookupResult':
        """Create an error result."""
        return cls(status="error", message=message)
    
    @classmethod
    def warning(cls, message: str, data: Optional[List[Dict[str, Any]]] = None) -> 'SupplierLookupResult':
        """Create a warning result."""
        return cls(status="warning", message=message, data=data or [])
