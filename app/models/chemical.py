from dataclasses import dataclass
from typing import List, Optional, Dict, Any


@dataclass
class Chemical:
    """
    Represents a chemical with its properties and HS code information.
    """
    name: str
    application: str
    hs_code: Optional[str] = None
    cas_number: Optional[str] = None
    product_family: Optional[str] = None
    hts_number: Optional[str] = None
    description: Optional[str] = None
    from_llm: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "name": self.name,
            "application": self.application,
            "hs_code": self.hs_code,
            "cas_number": self.cas_number,
            "product_family": self.product_family,
            "hts_number": self.hts_number,
            "description": self.description,
            "from_llm": self.from_llm
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Chemical':
        """Create Chemical instance from dictionary."""
        return cls(
            name=data.get("name", ""),
            application=data.get("application", ""),
            hs_code=data.get("hs_code"),
            cas_number=data.get("cas_number"),
            product_family=data.get("product_family"),
            hts_number=data.get("hts_number"),
            description=data.get("description"),
            from_llm=data.get("from_llm", False)
        )


@dataclass
class ChemicalLookupResult:
    """
    Represents the result of a chemical lookup operation.
    """
    status: str  # "success", "error", "warning"
    data: Optional[List[Dict[str, Any]]] = None
    message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        result = {
            "status": self.status
        }
        
        if self.data is not None:
            result["data"] = self.data
            
        if self.message is not None:
            result["message"] = self.message
            
        return result
    
    @classmethod
    def success(cls, data: List[Dict[str, Any]], message: Optional[str] = None) -> 'ChemicalLookupResult':
        """Create a successful result."""
        return cls(status="success", data=data, message=message)
    
    @classmethod
    def error(cls, message: str) -> 'ChemicalLookupResult':
        """Create an error result."""
        return cls(status="error", message=message)
    
    @classmethod
    def warning(cls, message: str, data: Optional[List[Dict[str, Any]]] = None) -> 'ChemicalLookupResult':
        """Create a warning result."""
        return cls(status="warning", message=message, data=data or [])


@dataclass
class ChemicalMatchResult:
    """
    Represents the result of matching chemical information from multiple sources.
    """
    hs_code_match: bool = False
    cas_number_match: bool = False
    product_family_match: bool = False
    confidence_score: float = 0.0
    final_hs_code: Optional[str] = None
    final_cas_number: Optional[str] = None
    final_product_family: Optional[str] = None
    source_preference: str = "none"  # "openai", "perplexity", "consensus"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "hs_code_match": self.hs_code_match,
            "cas_number_match": self.cas_number_match,
            "product_family_match": self.product_family_match,
            "confidence_score": self.confidence_score,
            "final_hs_code": self.final_hs_code,
            "final_cas_number": self.final_cas_number,
            "final_product_family": self.final_product_family,
            "source_preference": self.source_preference
        }
