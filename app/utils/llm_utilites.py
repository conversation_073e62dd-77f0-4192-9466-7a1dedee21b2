import requests
from app.services.perplexity_client import PerplexityClient
import json

class LLMUtils:

    def llm_extract_duty_params_pereplexity(query):
        prompt = f"""
            Extract the specified product name of the chemical, HTS number (10 digit full code) and 2-alphabet country code from this query: {query}.
            If import country is not explictly mentioned in the query then respond with "N/A" 
            If not specified or partially specified, suggest most relevant product name, HTS number for tariff duty calculation.

            You are a bot meant for only answering tariff related questions for US firms. 
            Respond with None for the json fields if the user query is not relevant in this context.
            
            For known chemical names in the list below, respond with the corresponding precified hts_number
            {{"Epoxy Resin":"3907.30.00.00","Methyl methacrylate":"2916.14.20.20","Alkyd Resins":"3907.50.00.00","MEA chemical":"2922.11.00.00","nitrocellulose":"3912.20.00.00","MEA TRIAZINE 78":"2933.69.20.00","Aminoethylethanolamine":"2922.19.09.00","AEEA":"2922.19.09.00","Acetic Acid":"2915.21.00.00","Tallow Diamine Ethoxylate":"3402.42.10.00","alkyl pyridine":"2933.31.00.00","QUAT":"2923.90.01.00","Imidazoline 2:1 Deta Tofa":"2933.29.90.00","Paraformaldehyde":"2912.60.00.00","Chain Stopped Short Oil Alkyd":"3907.50.00.00","Nitrocellulose":"3912.20.00.00","70% Coconut Alkyd":"3907.50.00.00","Acetone":"2914.11","Butyl Carbitol":"2909.43.00.00","Short oil alkyd resin":"3907.50.00.00","Titanium Dioxide":"3206.11.00.00"}}
            
            Respond ONLY with a JSON object in this exact format, with no additional text, comments or markdown:
            {{
                "product_name": "<product name>",
                "hts_number": "<10 digit HTS code>",
                "country_code": "<2 letter country code>"
            }}
        """

        payload = {
            "model": "sonar-pro",
            "messages": [
                {
                    "role": "system", 
                    "content": "You must respond only with a JSON object. No additional text, comments, or markdown formatting."
                },
                {"role": "user", "content": prompt}
            ]
        }

        try:
            response_json = PerplexityClient().query(payload)
            if not response_json:
                print("Error: API returned non 200 status")
                return {"product_name": None, "hts_number": None, "country_code": None}
            print(response_json)
            response_content = response_json["choices"][0]["message"]["content"]
            print(response_content)
            
            # Extract JSON from the response content
            # Look for JSON pattern between curly braces
            try:
                # Find the first { and last } in the content
                start_idx = response_content.find('{')
                end_idx = response_content.rfind('}')
                
                if start_idx != -1 and end_idx != -1:
                    json_str = response_content[start_idx:end_idx + 1]
                    # Remove any comments (text after #)
                    json_str = '\n'.join(line.split('#')[0].strip() for line in json_str.split('\n'))
                    parsed_content = json.loads(json_str)
                    
                    # Validate that all required fields are present
                    if all(key in parsed_content for key in ["product_name", "hts_number", "country_code"]):
                        return parsed_content
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON: {e}")
                    
            return {"product_name": None, "hts_number": None, "country_code": None}
            
        except requests.exceptions.RequestException as e:
            print(f"Request error: {e}")
            return {"product_name": None, "hts_number": None, "country_code": None}
        except Exception as e:
            print(f"Unexpected error: {e}")
            return {"product_name": None, "hts_number": None, "country_code": None}
