"""
Utility functions for formatting data display.
"""

def format_number(value, decimal_places=2):
    """
    Format a number with proper thousands separators and decimal places.
    
    Args:
        value: The number to format
        decimal_places: Number of decimal places to show
        
    Returns:
        Formatted string representation of the number
    """
    if value is None:
        return "N/A"
    
    try:
        # Convert to float if it's not already
        num_value = float(value)
        
        # Format with thousands separator and specified decimal places
        if decimal_places == 0:
            return f"{num_value:,.0f}"
        else:
            return f"{num_value:,.{decimal_places}f}"
    except (ValueError, TypeError):
        return str(value)

def format_currency(value, currency_symbol="$", decimal_places=2):
    """
    Format a number as currency.
    
    Args:
        value: The number to format
        currency_symbol: Currency symbol to use
        decimal_places: Number of decimal places to show
        
    Returns:
        Formatted currency string
    """
    if value is None:
        return "N/A"
    
    try:
        num_value = float(value)
        formatted_num = format_number(num_value, decimal_places)
        return f"{currency_symbol}{formatted_num}"
    except (ValueError, TypeError):
        return str(value)

def format_percentage(value, decimal_places=1):
    """
    Format a number as a percentage.
    
    Args:
        value: The number to format (should be between 0-100)
        decimal_places: Number of decimal places to show
        
    Returns:
        Formatted percentage string
    """
    if value is None:
        return "N/A"
    
    try:
        num_value = float(value)
        return f"{num_value:.{decimal_places}f}%"
    except (ValueError, TypeError):
        return str(value)

def format_weight(value, unit="tons", decimal_places=2):
    """
    Format a weight value with units.
    
    Args:
        value: The weight value to format
        unit: Unit of measurement
        decimal_places: Number of decimal places to show
        
    Returns:
        Formatted weight string
    """
    if value is None:
        return "N/A"
    
    try:
        num_value = float(value)
        formatted_num = format_number(num_value, decimal_places)
        return f"{formatted_num} {unit}"
    except (ValueError, TypeError):
        return str(value)

def truncate_text(text, max_length=50, suffix="..."):
    """
    Truncate text to a maximum length with optional suffix.
    
    Args:
        text: Text to truncate
        max_length: Maximum length of the text
        suffix: Suffix to add if text is truncated
        
    Returns:
        Truncated text string
    """
    if not text:
        return ""
    
    text_str = str(text)
    if len(text_str) <= max_length:
        return text_str
    
    return text_str[:max_length - len(suffix)] + suffix

def format_list_display(items, max_items=3, separator=", "):
    """
    Format a list for display, showing only the first few items.
    
    Args:
        items: List of items to format
        max_items: Maximum number of items to show
        separator: Separator between items
        
    Returns:
        Formatted string representation of the list
    """
    if not items:
        return "None"
    
    if len(items) <= max_items:
        return separator.join(str(item) for item in items)
    
    displayed_items = items[:max_items]
    remaining_count = len(items) - max_items
    
    result = separator.join(str(item) for item in displayed_items)
    result += f" (+{remaining_count} more)"
    
    return result