import json
from typing import Dict, List, Optional, Any, Set
import re
from collections import defaultdict

class HTSLookup:
    def __init__(self, data: List[dict]):
        """
        Initialize the HTS lookup system with data and build indices.
        
        Args:
            data (List[dict]): List of HTS code entries
        """
        self.data = data
        self.code_index = {}  # HTS code to position/details
        self.word_index = defaultdict(set)  # Word to set of HTS codes
        self._build_indices()
        
    def _build_indices(self):
        """Build both HTS code and description-based indices."""
        for position, item in enumerate(self.data):
            if item.get('htsno'):
                # Build code index
                self.code_index[item['htsno']] = {
                    'position': position,
                    'description': item.get('description', ''),
                    'general': item.get('general', ''),
                    'indent': item.get('indent', ''),
                    'units': item.get('units', [])
                }
                
                # Build word index from description
                if description := item.get('description'):
                    # Convert to lowercase and remove special characters
                    cleaned_desc = re.sub(r'[^\w\s]', ' ', description.lower())
                    # Split into words and remove empty strings
                    words = [w for w in cleaned_desc.split() if w]
                    
                    # Add each word to the index
                    for word in words:
                        self.word_index[word].add(item['htsno'])
    
    def search_by_description(self, query: str, max_results: int = 10) -> List[dict]:
        """
        Search for HTS codes by description text.
        
        Args:
            query (str): Search query
            max_results (int): Maximum number of results to return
            
        Returns:
            List[dict]: List of matching HTS entries with relevance scores
        """
        # Clean and split query
        cleaned_query = re.sub(r'[^\w\s]', ' ', query.lower())
        query_words = [w for w in cleaned_query.split() if w]
        
        # Find matching HTS codes for each word
        matching_codes = defaultdict(int)  # HTS code to match score
        
        for word in query_words:
            for hts_code in self.word_index.get(word, set()):
                matching_codes[hts_code] += 1
        
        # Sort by number of matching words (descending) and limit results
        sorted_results = sorted(
            matching_codes.items(),
            key=lambda x: (-x[1], x[0])  # Sort by score descending, then by HTS code
        )[:max_results]
        
        # Build detailed results
        results = []
        for hts_code, score in sorted_results:
            item = self.data[self.code_index[hts_code]['position']]
            results.append({
                'hts_code': hts_code,
                'description': item.get('description', ''),
                'match_score': score,
                'match_percentage': (score / len(query_words)) * 100,
                'general': item.get('general', ''),
                'special': item.get('special', ''),
                'other': item.get('other', '')
            })
        
        return results
    
    def lookup_hts(self, hts_code: str) -> Optional[dict]:
        """
        Look up details for a specific HTS code including any footnote references.
        
        Args:
            hts_code (str): The HTS code to look up
            
        Returns:
            Optional[dict]: Dictionary containing the HTS code details and footnote references
        """
        if hts_code not in self.code_index:
            return None
            
        item = self.data[self.code_index[hts_code]['position']]
        
        result = {
            'main_details': {
                'htsno': item.get('htsno', ''),
                'description': item.get('description', ''),
                'units': item.get('units', []),
                'general': item.get('general', ''),
                'special': item.get('special', ''),
                'other': item.get('other', ''),
                'indent': item.get('indent', ''),
                'superior': item.get('superior', '')
            },
            'footnote_references': []
        }
        
        # Process footnotes
        if item.get('footnotes'):
            for footnote in item['footnotes']:
                if isinstance(footnote, dict) and 'columns' in footnote:
                    if 'general' in footnote['columns']:
                        footnote_value = footnote.get('value', '')
                        
                        if 'See ' in footnote_value:
                            referenced_hts = footnote_value.split('See ')[1].strip('.')
                            referenced_hts = ".".join(referenced_hts.split(".")[:-1])
                            if referenced_hts in self.code_index:
                                ref_position = self.code_index[referenced_hts]['position']
                                ref_item = self.data[ref_position]
                                
                                result['footnote_references'].append({
                                    'referenced_code': referenced_hts,
                                    'footnote_value': footnote_value,
                                    'details': {
                                        'htsno': ref_item.get('htsno', ''),
                                        'description': ref_item.get('description', ''),
                                        'general': ref_item.get('general', ''),
                                        'special': ref_item.get('special', ''),
                                        'other': ref_item.get('other', '')
                                    }
                                })
        
        return result

    def get_index_stats(self) -> dict:
        """
        Get statistics about the indices.
        
        Returns:
            dict: Statistics about both indices
        """
        return {
            'total_entries': len(self.data),
            'indexed_hts_codes': len(self.code_index),
            'indexed_words': len(self.word_index),
            'average_codes_per_word': sum(len(codes) for codes in self.word_index.values()) / len(self.word_index) if self.word_index else 0,
            'memory_usage': {
                'code_index_size': len(self.code_index),
                'word_index_size': len(self.word_index),
                'unique_words': len(set(self.word_index.keys()))
            }
        }



# Example usage:
if __name__ == "__main__":
    # Load data and create lookup system
    with open('htsdata_all.json', 'r') as file:
        hts_data = json.load(file)
    
    # Create lookup system
    lookup = HTSLookup(hts_data)
    
    # Example code lookup
    code_result = lookup.lookup_hts("3901.10.10.00")
    print("Code lookup result:", json.dumps(code_result, indent=2))
    
    # Example description search
    desc_results = lookup.search_by_description("epoxy resins")
    print("\nDescription search results:", json.dumps(desc_results, indent=2))