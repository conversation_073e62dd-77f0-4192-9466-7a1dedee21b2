"""chat_history_request_headers

Revision ID: 6aa73a3d8b05
Revises: 5cbe1bd0f210
Create Date: 2025-02-16 01:21:09.274867

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6aa73a3d8b05'
down_revision = '5cbe1bd0f210'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('chat_history', schema=None) as batch_op:
        batch_op.alter_column('request_headers',
               existing_type=sa.VARCHAR(length=5000),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('chat_history', schema=None) as batch_op:
        batch_op.alter_column('request_headers',
               existing_type=sa.VARCHAR(length=5000),
               nullable=False)

    # ### end Alembic commands ###
