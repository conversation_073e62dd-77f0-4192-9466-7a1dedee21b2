"""Baseline migration

Revision ID: 5cbe1bd0f210
Revises: 
Create Date: 2025-02-16 01:14:16.245442

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5cbe1bd0f210'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chat_history',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=False),
    sa.Column('sender_type', sa.String(length=50), nullable=False),
    sa.Column('message', sa.String(length=5000), nullable=False),
    sa.Column('request_headers', sa.String(length=5000), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.<PERSON>KeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('chat_history')
    # ### end Alembic commands ###
